import { LitElement, html } from 'lit-element';
import { getComponentSharedStyles } from '@bbva-web-components/bbva-core-lit-helpers';
import styles from './my-custom-element.css.js';
import { tableData } from '../mocks/table-data.js';
import '@bbva-web-components/bbva-core-icon';
import '@bbva-web-components/bbva-web-link';
import '@bbva-web-components/bbva-web-table';
import '@bbva-web-components/bbva-web-amount';
import '@bbva-web-components/bbva-web-amount/bbva-web-amount.js';
import '@bbva-web-components/bbva-web-navigation-contextual-box';
import '@bbva-web-components/bbva-foundations-styles';
import '@bbva-web-components/bbva-foundations-icons';

/**
 * ![LitElement component](https://img.shields.io/badge/LitElement-component-blue.svg)
 *
 * This component ...
 *
 * Example:
 *
 * ```html
 *   <my-custom-element></my-custom-element>
 * ```
 */
export class MyCustomElement extends LitElement {
  static get properties() {
    return {
      showAllRows: { type: Boolean }
    };
  }

  constructor() {
    super();
    this.showAllRows = false;
  }

  firstUpdated() {
    this._setupContextualMenus();
  }

  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has('showAllRows')) {
      // Re-configurar los menús cuando cambie la vista
      setTimeout(() => {
        this._setupContextualMenus();
      }, 0);
    }
  }

  _setupContextualMenus() {
    // Limpiar event listeners anteriores si existen
    if (this._documentClickHandler) {
      document.removeEventListener('click', this._documentClickHandler);
    }

    const actionMenus = this.shadowRoot.querySelectorAll('.action-menu');
    actionMenus.forEach(menu => {
      const trigger = menu.querySelector('.menu-trigger');
      if (trigger) {
        // Remover event listener anterior si existe
        if (trigger._clickHandler) {
          trigger.removeEventListener('click', trigger._clickHandler);
        }

        // Crear nuevo event listener
        trigger._clickHandler = (e) => {
          e.stopPropagation();
          // Cerrar otros menús abiertos
          actionMenus.forEach(otherMenu => {
            if (otherMenu !== menu) {
              otherMenu.classList.remove('open');
            }
          });
          // Toggle del menú actual
          menu.classList.toggle('open');
        };

        trigger.addEventListener('click', trigger._clickHandler);
      }
    });

    // Cerrar menús al hacer clic fuera
    this._documentClickHandler = () => {
      actionMenus.forEach(menu => {
        menu.classList.remove('open');
      });
    };

    document.addEventListener('click', this._documentClickHandler);
  }

  _toggleShowMore() {
    this.showAllRows = !this.showAllRows;
  }

  _editRow(cuentaDestino) {
    // Encontrar el registro a editar
    const index = tableData.findIndex(row => row.cuentaDestino === cuentaDestino);

    if (index !== -1) {
      const row = tableData[index];
      console.log(`📝 Editando registro:`, row);

      // Editar Cuenta de Destino
      const newCuentaDestino = window.prompt("Cuenta de destino:", row.cuentaDestino);
      if (newCuentaDestino === null) return; // Usuario canceló

      // Editar Destinatario
      const newDestinatario = window.prompt("Destinatario:", row.destinatario);
      if (newDestinatario === null) return; // Usuario canceló

      // Editar Concepto de Pago
      const newConceptoPago = window.prompt("Concepto de pago:", row.conceptoPago);
      if (newConceptoPago === null) return; // Usuario canceló

      // Editar Importe
      const newImporte = window.prompt("Importe:", row.importe);
      if (newImporte === null) return; // Usuario canceló

      // Editar Código de Moneda
      const newCurrencyCode = window.prompt("Código de moneda (PEN, USD, EUR):", row.currencyCode);
      if (newCurrencyCode === null) return; // Usuario canceló

      // Actualizar el registro en el mock
      tableData[index] = {
        cuentaDestino: newCuentaDestino.trim(),
        destinatario: newDestinatario.trim(),
        conceptoPago: newConceptoPago.trim(),
        importe: newImporte.trim(),
        currencyCode: newCurrencyCode.trim().toUpperCase()
      };

      console.log(`✅ Registro actualizado:`, tableData[index]);

      // Forzar re-render del componente
      this.requestUpdate();

      // Re-configurar menús después del re-render
      setTimeout(() => {
        this._setupContextualMenus();
      }, 0);
    } else {
      console.log(`❌ No se encontró el registro con cuenta: ${cuentaDestino}`);
    }
  }

  _handleEditClick(event, cuentaDestino) {
    event.preventDefault();
    event.stopPropagation();

    // Cerrar el menú contextual
    const actionMenus = this.shadowRoot.querySelectorAll('.action-menu');
    actionMenus.forEach(menu => {
      menu.classList.remove('open');
    });

    // Editar el registro
    this._editRow(cuentaDestino);
  }

  _handleDeleteClick(event, cuentaDestino) {
    event.preventDefault();
    event.stopPropagation();

    // Cerrar el menú contextual
    const actionMenus = this.shadowRoot.querySelectorAll('.action-menu');
    actionMenus.forEach(menu => {
      menu.classList.remove('open');
    });

    // Mostrar modal de confirmación nativo del navegador
    const confirmMessage = "¿Estás seguro que quieres eliminar este registro?";

    if (window.confirm(confirmMessage)) {
      this._deleteRow(cuentaDestino);
    }
  }



  _deleteRow(cuentaDestino) {
    console.log(`Intentando eliminar cuenta: ${cuentaDestino}`);
    console.log(`Total registros antes: ${tableData.length}`);

    // Encontrar el índice del registro a eliminar
    const index = tableData.findIndex(row => row.cuentaDestino === cuentaDestino);
    console.log(`Índice encontrado: ${index}`);

    if (index !== -1) {
      // Eliminar del mock
      const deletedRow = tableData.splice(index, 1);
      console.log(`Registro eliminado:`, deletedRow[0]);
      console.log(`Total registros después: ${tableData.length}`);

      // Si estamos mostrando solo 3 registros y quedan menos de 3, ajustar la vista
      if (!this.showAllRows && tableData.length < 3) {
        // Si quedan menos de 3 registros, mostrar todos
        if (tableData.length > 0) {
          this.showAllRows = false; // Mantener la vista de "primeros registros"
        }
      }

      // Forzar re-render del componente
      this.requestUpdate();

      // Re-configurar menús después del re-render
      setTimeout(() => {
        this._setupContextualMenus();
      }, 0);

      console.log(`✅ Eliminación completada. Registros restantes: ${tableData.length}`);
    } else {
      console.log(`❌ No se encontró el registro con cuenta: ${cuentaDestino}`);
    }
  }

  static get styles() {
    return [
      styles,
      getComponentSharedStyles('my-custom-element-shared-styles'),
    ];
  }

  _renderTableRows() {
    const dataToShow = this.showAllRows ? tableData : tableData.slice(0, 3);
    console.log(`🔄 Renderizando ${dataToShow.length} filas de ${tableData.length} total`);
    return dataToShow.map((row) => html`
      <tr>
        <td><bbva-web-table-body-text>${row.cuentaDestino}</bbva-web-table-body-text></td>
        <td><bbva-web-table-body-text>${row.destinatario}</bbva-web-table-body-text></td>
        <td><bbva-web-table-body-text>${row.conceptoPago}</bbva-web-table-body-text></td>
        <td>
          <bbva-web-amount
            amount="${row.importe}"
            currency-code="${row.currencyCode}"
            local-currency="${row.currencyCode}"
            symbol-left="">
          </bbva-web-amount>
        </td>
        <td>
          <div class="action-menu">
            <span class="menu-trigger">⋮</span>
            <div class="menu-content">
              <bbva-web-link @click="${(e) => this._handleEditClick(e, row.cuentaDestino)}" href="#">Editar</bbva-web-link>
              <bbva-web-link @click="${(e) => this._handleDeleteClick(e, row.cuentaDestino)}" href="#">Eliminar</bbva-web-link>
            </div>
          </div>
        </td>
      </tr>
    `);
  }

  render() {
    return html`
      <slot></slot>
      <table>
        <thead>
          <tr>
            <th><bbva-web-table-header-text>CUENTA DE DESTINO</bbva-web-table-header-text></th>
            <th><bbva-web-table-header-text>DESTINATARIO</bbva-web-table-header-text></th>
            <th><bbva-web-table-header-text>CONCEPTO DE PAGO</bbva-web-table-header-text></th>
            <th><bbva-web-table-header-text>IMPORTE</bbva-web-table-header-text></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          ${this._renderTableRows()}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="5">
              <bbva-web-table-footer variant="prefooter">
                <bbva-web-link @click="${this._toggleShowMore}" icon="${this.showAllRows ? 'bbva:chevron-up' : 'bbva:chevron-down'}">
                  ${this.showAllRows ? 'Ver menos' : 'Ver más'}
                </bbva-web-link>
              </bbva-web-table-footer>
            </td>
          </tr>
        </tfoot>
      </table>
    `;
  }
}
