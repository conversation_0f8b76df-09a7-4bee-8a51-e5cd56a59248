import { LitElement, html } from 'lit-element';
import { getComponentSharedStyles } from '@bbva-web-components/bbva-core-lit-helpers';
import styles from './my-custom-element.css.js';
import { tableData } from '../mocks/table-data.js';
import '@bbva-web-components/bbva-core-icon';
import '@bbva-web-components/bbva-web-link';
import '@bbva-web-components/bbva-web-table';
import '@bbva-web-components/bbva-web-amount';
import '@bbva-web-components/bbva-web-amount/bbva-web-amount.js';
import '@bbva-web-components/bbva-web-navigation-contextual-box';
import '@bbva-web-components/bbva-foundations-styles';
import '@bbva-web-components/bbva-foundations-icons';

/**
 * ![LitElement component](https://img.shields.io/badge/LitElement-component-blue.svg)
 *
 * This component ...
 *
 * Example:
 *
 * ```html
 *   <my-custom-element></my-custom-element>
 * ```
 */
export class MyCustomElement extends LitElement {
  static get properties() {
    return {

    };
  }

  constructor() {
    super();
  }

  firstUpdated() {
    this._setupContextualMenus();
  }

  _setupContextualMenus() {
    const actionMenus = this.shadowRoot.querySelectorAll('.action-menu');
    actionMenus.forEach(menu => {
      const trigger = menu.querySelector('.menu-trigger');
      if (trigger) {
        trigger.addEventListener('click', (e) => {
          e.stopPropagation();
          // Cerrar otros menús abiertos
          actionMenus.forEach(otherMenu => {
            if (otherMenu !== menu) {
              otherMenu.classList.remove('open');
            }
          });
          // Toggle del menú actual
          menu.classList.toggle('open');
        });
      }
    });

    // Cerrar menús al hacer clic fuera
    document.addEventListener('click', () => {
      actionMenus.forEach(menu => {
        menu.classList.remove('open');
      });
    });
  }



  static get styles() {
    return [
      styles,
      getComponentSharedStyles('my-custom-element-shared-styles'),
    ];
  }

  _renderTableRows() {
    return tableData.map((row) => html`
      <tr>
        <td><bbva-web-table-body-text>${row.cuentaDestino}</bbva-web-table-body-text></td>
        <td><bbva-web-table-body-text>${row.destinatario}</bbva-web-table-body-text></td>
        <td><bbva-web-table-body-text>${row.conceptoPago}</bbva-web-table-body-text></td>
        <td>
          <bbva-web-amount
            amount="${row.importe}"
            currency-code="${row.currencyCode}"
            local-currency="${row.currencyCode}"
            symbol-left="">
          </bbva-web-amount>
        </td>
        <td>
          <div class="action-menu">
            <span class="menu-trigger">⋮</span>
            <div class="menu-content">
              <bbva-web-link href="#">Editar</bbva-web-link>
              <bbva-web-link href="#">Eliminar</bbva-web-link>
            </div>
          </div>
        </td>
      </tr>
    `);
  }

  render() {
    return html`
      <slot></slot>
      <table>
        <thead>
          <tr>
            <th><bbva-web-table-header-text>CUENTA DE DESTINO</bbva-web-table-header-text></th>
            <th><bbva-web-table-header-text>DESTINATARIO</bbva-web-table-header-text></th>
            <th><bbva-web-table-header-text>CONCEPTO DE PAGO</bbva-web-table-header-text></th>
            <th><bbva-web-table-header-text>IMPORTE</bbva-web-table-header-text></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          ${this._renderTableRows()}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="5">
              <bbva-web-table-footer variant="prefooter"> 
                <bbva-web-link href="#" icon="bbva:chevron-down">Ver más</bbva-web-link>
              </bbva-web-table-footer>
            </td>
          </tr>
        </tfoot>
      </table>
    `;
  }
}
