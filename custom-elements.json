{"schemaVersion": "1.0.0", "readme": "", "package": "@cells-custom-components/my-custom-element", "modules": [{"kind": "javascript-module", "path": "index.js", "declarations": [], "exports": [{"kind": "js", "name": "MyCustomElement", "declaration": {"name": "MyCustomElement", "module": "./src/MyCustomElement.js"}}], "entrypoint": "\\index.js", "defaultEntrypoint": true}, {"kind": "javascript-module", "path": "my-custom-element.js", "declarations": [], "exports": [{"kind": "custom-element-definition", "name": "my-custom-element", "declaration": {"name": "MyCustomElement", "module": "/src/MyCustomElement.js"}}], "entrypoint": "my-custom-element.js", "defaultEntrypoint": false}, {"kind": "javascript-module", "path": "src/my-custom-element.css.js", "declarations": [{"kind": "js", "name": "default", "cssCustomProperties": []}], "exports": [{"kind": "js", "name": "default", "declaration": {"module": "src/my-custom-element.css.js"}}]}, {"kind": "javascript-module", "path": "src/MyCustomElement.js", "declarations": [{"kind": "class", "description": "![LitElement component](https://img.shields.io/badge/LitElement-component-blue.svg)\n\nThis component ...\n\nExample:\n\n```html\n  <my-custom-element></my-custom-element>\n```", "name": "MyCustomElement", "members": [{"kind": "field", "name": "name", "privacy": "public", "type": {"text": "string"}, "description": "Description for property", "default": "\"Cells\"", "attribute": "name"}], "attributes": [{"name": "name", "type": {"text": "string"}, "description": "Description for property", "default": "\"Cells\"", "fieldName": "name"}], "superclass": {"name": "LitElement", "package": "lit-element"}, "tagName": "my-custom-element", "customElement": true}], "exports": [{"kind": "js", "name": "MyCustomElement", "declaration": {"name": "MyCustomElement", "module": "src/MyCustomElement.js"}}]}], "packageName": "@cells-custom-components/my-custom-element"}