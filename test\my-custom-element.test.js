import '@bbva-web-components/bbva-core-scoped-custom-element-registry';
import {
  html,
  fixture,
  assert,
  fixtureCleanup
} from '@open-wc/testing';
import '../my-custom-element.js';

suite('MyCustomElement', () => {
  let el;

  teardown(() => fixtureCleanup());

  suite('default', () => {
    setup(async () => {
      el = await fixture(html`
        <my-custom-element></my-custom-element>
      `);
      await el.updateComplete;
    });

    test('a11y', async () => {
      await assert.isAccessible(el);
    });
  });
});
