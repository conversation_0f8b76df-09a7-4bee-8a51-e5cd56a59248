# @cells-custom-components/my-custom-element

## Package info

### Package installation

Installation using NPM

```bash
npm install @cells-custom-components/my-custom-element
```

### Entry points & exports

- (Default entry point)
  - MyCustomElement (Class)
- my-custom-element.js
  - my-custom-element (Custom Element)


## MyCustomElement (Class) my-custom-element (Custom Element) 

### Extends from

LitElement (lit-element package)

### Usage

Import and extend the class:

```js
import { MyCustomElement } from '@cells-custom-components/my-custom-element';

class ExampleElement extends MyCustomElement {
  ...
}
```

Use the custom element (defined globally):

```js
import '@cells-custom-components/my-custom-element/my-custom-element.js';
```

```html
<my-custom-element ...>
  ...
</my-custom-element>
```

### Description

![LitElement component](https://img.shields.io/badge/LitElement-component-blue.svg)

This component ...

Example:

```html
  <my-custom-element></my-custom-element>
```

### Properties

- **name**: string = "Cells" (attribute: name)
    Description for property
