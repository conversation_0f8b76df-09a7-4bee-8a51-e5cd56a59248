import { css, unsafeCSS } from 'lit-element';
import * as foundations from '@bbva-web-components/bbva-foundations-styles';

export default css`
:host {
  display: block;
  box-sizing: border-box;
}

:host([hidden]), [hidden] {
  display: none !important;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* Estilos para la tabla */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px auto;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Estilos para el header */
thead {
  background-color: #f8f9fa;
}

thead th {
  padding: 16px;
  text-align: left;
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
  color: #495057;
}

/* Estilos para el body */
tbody tr {
  border-bottom: 1px solid #e9ecef;
}

tbody tr:hover {
  background-color: #f8f9fa;
}

tbody td {
  padding: 16px;
  vertical-align: middle;
}

/* Estilos para los menús de acción */
.action-menu {
  position: relative;
  display: inline-block;
}

.menu-trigger {
  cursor: pointer;
  padding: 8px;
  font-size: 18px;
  color: #007bff;
  user-select: none;
}

.menu-trigger:hover {
  color: #0056b3;
}

.menu-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  z-index: 1000;
  padding: 8px 0;
}

.action-menu.open .menu-content {
  display: block;
}

.menu-content bbva-web-link {
  display: block;
  padding: 8px 16px;
  text-decoration: none;
  color: #495057;
}

.menu-content bbva-web-link:hover {
  background-color: #f8f9fa;
}

/* Estilos para bbva-web-amount */
bbva-web-amount {
  color: #212529 !important;
  font-weight: normal !important;
  display: inline-block !important;
  font-size: 14px !important;
}
`;
