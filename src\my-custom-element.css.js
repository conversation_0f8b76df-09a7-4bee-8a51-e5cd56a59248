import { css, unsafeCSS } from 'lit-element';
import * as foundations from '@bbva-web-components/bbva-foundations-styles';

export default css`
:host {
  display: block;
  box-sizing: border-box;
  padding: 24px;
}

:host([hidden]), [hidden] {
  display: none !important;
}

*, *::before, *::after {
  box-sizing: inherit;
}

/* Estilos para la tabla */
table {
  width: 100%;
  border-collapse: collapse;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 32px auto;
  max-width: 1200px;
}

/* Estilos para el header */
thead {
  background-color: #f8f9fa;
}

thead th {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  font-weight: normal;
  font-size: 14px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Estilos para el body */
tbody tr {
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
}

tbody tr:hover {
  background-color: #f8f9fa;
}

tbody td {
  padding: 16px 20px;
  vertical-align: middle;
  font-size: 14px;
  color: #212529;
}

/* Estilos específicos para las columnas */
tbody td:first-child {
  color: #0073e6;
  font-weight: 500;
}

tbody td:nth-child(2),
tbody td:nth-child(3),
tbody td:nth-child(4),
tbody td:nth-child(5) {
  font-weight: 600;
  text-align: left;
}

/* Estilo para el texto de tabla - color negro sin negrita */
bbva-web-table-body-text {
  color: #212529 !important;
  text-decoration: none;
  font-weight: normal !important;
}

/* Asegurar que todos los textos de tabla sean negros sin negrita */
tbody td {
  color: #212529 !important;
  font-weight: normal !important;
}

/* Asegurar que los importes sean negros sin negrita */
bbva-web-amount {
  color: #212529 !important;
  font-weight: normal !important;
  display: inline-block !important;
  font-size: 14px !important;
}

/* Estilos específicos para bbva-web-amount en tablas */
tbody td bbva-web-amount {
  color: #212529 !important;
  font-weight: normal !important;
  display: inline-block !important;
}

/* Estilo para los enlaces de número de contrato */
bbva-web-link {
  color: #0073e6;
  text-decoration: none;
  font-weight: 500;
}

bbva-web-link:hover {
  text-decoration: underline;
}

/* Estilos para el footer */
tfoot td {
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background-color: #ffffff;
}

/* Estilos para el enlace "Ver más" */
bbva-web-link {
  color: #0073e6;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
}

bbva-web-link:hover {
  text-decoration: underline;
}

/* Estilos para el menú contextual de 3 puntos */
.action-menu {
  position: relative;
  display: inline-block;
  text-align: left;
}

.menu-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 1000;
  padding: 8px 0;
}

.menu-content bbva-web-link {
  display: block;
  padding: 8px 16px;
  color: #0073e6;
  text-decoration: none;
  font-size: 16px;
  font-weight: normal;
}

.menu-content bbva-web-link:hover {
  background-color: #f8f9fa;
}

.action-menu.open .menu-content {
  display: block;
}

/* Icono de 3 puntos */
.menu-trigger {
  cursor: pointer;
  padding: 8px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-size: 24px;
  font-weight: bold;
  color: #007bff !important;
  display: inline-block;
  line-height: 1;
  user-select: none;
  text-align: left;
}

.menu-trigger:hover {
  background-color: #f8f9fa;
  color: #007bff !important;
}

/* Estilo específico para los 3 puntos en la última columna */
tbody td:last-child .menu-trigger {
  color: #007bff !important;
}

tbody td:last-child .menu-trigger:hover {
  color: #007bff !important;
}

/* Estilo directo para el span de 3 puntos */
span.menu-trigger {
  color: #007bff !important;
}

span.menu-trigger:hover {
  color: #007bff !important;
}

/* Estilo ultra específico para los 3 puntos */
.action-menu .menu-trigger {
  color: #007bff !important;
}

.action-menu .menu-trigger:hover {
  color: #007bff !important;
}

/* Estilo para los importes que simula bbva-web-amount */
.amount-text {
  font-weight: 600 !important;
  color: #212529 !important;
  font-family: 'Benton Sans', Arial, sans-serif;
}

/* Ajustar columna de acciones - alineada a la izquierda */
thead th:last-child {
  width: 60px;
  text-align: left;
  padding-left: 0;
}

/* Ajustar celdas de acciones - alineadas a la izquierda */
tbody td:last-child {
  text-align: left;
  padding-left: 0;
  width: 60px;
}

/* Alinear columna IMPORTE a la derecha */
thead th:nth-last-child(2) {
  text-align: right;
  padding-right: 8px;
}

tbody td:nth-last-child(2) {
  text-align: right;
  padding-right: 8px;
}

/* Responsive */
@media (max-width: 768px) {
  table {
    font-size: 12px;
  }

  thead th,
  tbody td,
  tfoot td {
    padding: 12px 16px;
  }

  tbody td:nth-child(4) {
    text-align: left;
  }
}
`;
