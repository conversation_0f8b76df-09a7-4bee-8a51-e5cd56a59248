{"name": "@cells-custom-components/my-custom-element", "version": "1.0.0-rc.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@cells-custom-components/my-custom-element", "version": "1.0.0-rc.1", "dependencies": {"@bbva-web-components/bbva-core-icon": "^1.5.7", "@bbva-web-components/bbva-core-lit-helpers": "^1.0.0", "@bbva-web-components/bbva-foundations-icons": "^1.16.0", "@bbva-web-components/bbva-foundations-styles": "^2.0.0", "@bbva-web-components/bbva-web-amount": "^1.5.0", "@bbva-web-components/bbva-web-link": "^1.6.4", "@bbva-web-components/bbva-web-navigation-contextual-box": "^1.2.4", "@bbva-web-components/bbva-web-table": "^1.6.0", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}, "devDependencies": {"@bbva-web-components/bbva-core-scoped-custom-element-registry": "^0.1.0", "@bbva-web-components/bbva-dev-demo-helper": "^1.0.0", "@bbva-web-components/bbva-dev-demo-theme": "^2.0.0", "@bbva-web-components/bbva-foundations-sass": "^1.0.0", "@open-wc/testing": "^3.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@bbva-experience/tokens": {"version": "2.7.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-experience/tokens/-/@bbva-experience/tokens-2.7.0.tgz", "integrity": "sha512-gf/IW5cweoGg34vRI0C6dQaSSwV2jfU/Qv0ZovDQsynn2MusFCPBNX4+v5RFtYvH4Bmm83CNSK7OyoHAkusy7g==", "dev": true}, "node_modules/@bbva-experience/tokens-spherica": {"version": "1.8.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-experience/tokens-spherica/-/@bbva-experience/tokens-spherica-1.8.0.tgz", "integrity": "sha512-+OGqNtzAB5gcrfxPxR3CekmDGAfhYC377BPgCAgM4xa/ZyR3rJnR6j+SVPA2YW14wBNhf5AgHOh/oU8gkT7uYw==", "dev": true}, "node_modules/@bbva-web-components/bbva-core-action": {"version": "3.2.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-action/-/@bbva-web-components/bbva-core-action-3.2.2.tgz", "integrity": "sha512-as10iWBAUlDIhRdx3wEcJk1+avS5txaLTivkJMXeJkG5uJIkV/7PhJtLJ1So1smkEnE3BtSJjsxw1rkZMOTAyw==", "dependencies": {"@bbva-web-components/bbva-core-delegate-attributes-mixin": "^1.2.2", "@bbva-web-components/bbva-core-focus-visible-mixin": "^1.1.2", "@bbva-web-components/bbva-core-form-association-mixin": "^1.1.2", "@bbva-web-components/bbva-core-lit-helpers": "^1.6.2", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-core-amount-currency-mixin": {"version": "1.1.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-amount-currency-mixin/-/@bbva-web-components/bbva-core-amount-currency-mixin-1.1.4.tgz", "integrity": "sha512-ZHLRt/N95pUXtFsaM+N84ucAd42m7tfB05ar4+a3ymRjZ6YPgTud0CiGQ1Vc4hQenPkts1kic21WS5nt6NDuyA==", "dependencies": {"@bbva-web-components/bbva-core-amount-mixin": "^1.7.2", "@bbva-web-components/bbva-core-lit-helpers": "^1.6.2"}}, "node_modules/@bbva-web-components/bbva-core-amount-format-mixin": {"version": "1.3.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-amount-format-mixin/-/@bbva-web-components/bbva-core-amount-format-mixin-1.3.4.tgz", "integrity": "sha512-97/yp0o3/xGiQTjlMxbLFUm0LJsdNMrjtP06/ft6jaiTcxw2cIFV1uraPCupuxx2W/WOFr3VtQ061O6FMBawcA==", "dependencies": {"@bbva-web-components/bbva-core-amount-mixin": "^1.7.2", "@bbva-web-components/bbva-core-intl-mixin": "^1.8.4", "@bbva-web-components/bbva-core-lit-helpers": "^1.6.2"}}, "node_modules/@bbva-web-components/bbva-core-amount-mixin": {"version": "1.7.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-amount-mixin/-/@bbva-web-components/bbva-core-amount-mixin-1.7.2.tgz", "integrity": "sha512-LLLx4G4rFVt2J3khUpi/JiL9P2/uxd3TPUS6L1dLjL4GRZNHm19YHUbDy4vY61fWnesmPSv4oGsuswJjyThTXA==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.6.2"}}, "node_modules/@bbva-web-components/bbva-core-delegate-attributes-mixin": {"version": "1.2.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-delegate-attributes-mixin/-/@bbva-web-components/bbva-core-delegate-attributes-mixin-1.2.2.tgz", "integrity": "sha512-GyRCZP8uya6b2vu81q3UmAw6AdShe+IT4++SSOn71To0R6UNMw1lUjICHmGQrkZ6oOiRTSf4+WXf+KVL27uxlg==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.6.2", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-core-focus-visible-mixin": {"version": "1.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-focus-visible-mixin/-/@bbva-web-components/bbva-core-focus-visible-mixin-1.1.2.tgz", "integrity": "sha512-gxLZdp1NnFIXMYRcRnvIflWeeuoR8waTJfhpcIp4O6ZaXhnNQ8StCuPzISqwOfuoMp9qzDFOD6QmFqUwM4GsWw==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.6.2"}}, "node_modules/@bbva-web-components/bbva-core-form-association-mixin": {"version": "1.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-form-association-mixin/-/@bbva-web-components/bbva-core-form-association-mixin-1.1.2.tgz", "integrity": "sha512-6syyKx53eRSNKjT20C7+5gRCAb4OJ4kvJIO4rgUNo+UIYmngLtg++kjdhRB+TShK0pjwYgiaNxW3IMxdu9AzWw==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.6.2", "lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-core-generic-dp": {"version": "2.3.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-generic-dp/-/@bbva-web-components/bbva-core-generic-dp-2.3.3.tgz", "integrity": "sha512-3hg6QrwxsvOmVr2xqnloJz5vny3bGWpseFWNtvojJLojGorV4g6fB8Imm54RN7EWtg8DGFbCnp9PQmqsBqLRpA==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.6.2", "cells-mobile-plugins": "^1.1.1", "lit-element": "^2.0.0", "lit-html": "^1.0.0", "rxjs": "^6.5.2"}}, "node_modules/@bbva-web-components/bbva-core-icon": {"version": "1.6.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-icon/-/@bbva-web-components/bbva-core-icon-1.6.2.tgz", "integrity": "sha512-u8xSPc2mmxwf/Cb1FuKf5ohmWuCVCJn335tFN/7+NWTTDYyCCsWvMyjhBYGZH1ZHd42b+KVX4jEZ0REnY3CSNw==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.6.2", "@bbva-web-components/bbva-core-meta": "^1.3.2", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-core-iconset": {"version": "1.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-iconset/-/@bbva-web-components/bbva-core-iconset-1.3.0.tgz", "integrity": "sha512-cZbnpQnOFVRhtEmM+98FqNtqUOa42aKIIGcMjkvVYghtEHaGV1ps8vVgd6OfnKeEnosiDyZ9k0ja0BVOjDFxtA==", "dependencies": {"@bbva-web-components/bbva-core-meta": "^1.3.2", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-core-intl-mixin": {"version": "1.8.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-intl-mixin/-/@bbva-web-components/bbva-core-intl-mixin-1.8.4.tgz", "integrity": "sha512-9GI3Otdfl/0aBPq7jQdh0KGJQVA6vF+nQDrMbLqMjYmQxQCqEp+6NmUpIxv4Vuqj3PDfFbTDmT/xqGFizUNiSg==", "dependencies": {"@bbva-web-components/bbva-core-generic-dp": "^2.3.3", "@bbva-web-components/bbva-core-lit-helpers": "^1.6.2", "intl-messageformat": "~8.0.0"}}, "node_modules/@bbva-web-components/bbva-core-lit-directives": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-lit-directives/-/@bbva-web-components/bbva-core-lit-directives-1.0.1.tgz", "integrity": "sha512-xoEFE6VFpRJspgY15ksc2KRJurDhqCpA6teEuY5qAijxSUI/02LmiG/UTfOkSocxiGwLr9/ph9fzcwZ3BRLaMQ==", "dev": true, "dependencies": {"lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-core-lit-helpers": {"version": "1.6.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-lit-helpers/-/@bbva-web-components/bbva-core-lit-helpers-1.6.2.tgz", "integrity": "sha512-khTJGTc8nWgAQJCNubrx10cMD2u+9yi4NZo0MadC5jOTvGrjy3hMFz7SX7yD96kzHmwK7HTv7amEXqrteA/sQQ==", "dependencies": {"@bbva-web-components/bbva-core-meta": "^1.3.2", "@open-wc/dedupe-mixin": "^1.4.0", "@webcomponents/shadycss": "^1.9.0", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-core-meta": {"version": "1.3.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-meta/-/@bbva-web-components/bbva-core-meta-1.3.2.tgz", "integrity": "sha512-yzsrbU4YGQ73VVy64SAONSdw000dfMVGjp9/D+YzBQgsbgVxb1AS8BIpwoNWTsMWUfa2ZKog5X3WoAXBWzMXjw=="}, "node_modules/@bbva-web-components/bbva-core-scoped-custom-element-registry": {"version": "0.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-scoped-custom-element-registry/-/@bbva-web-components/bbva-core-scoped-custom-element-registry-0.1.2.tgz", "integrity": "sha512-xrkb1y/T8qYY5fLig7pBE6CiocW4r0AGbgIFR2d8J6rF3z1gOU5Kk/zm5RdSGuqtCa1jWp8hAuDHVpnxKZFLQQ==", "dev": true}, "node_modules/@bbva-web-components/bbva-core-theme-style-helpers": {"version": "1.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-theme-style-helpers/-/@bbva-web-components/bbva-core-theme-style-helpers-1.0.5.tgz", "integrity": "sha512-RhxDHn5XcN2w2RAtHq6ZMirkcdqwU8k+hXSyfalKuYBp278JjH1NtHmZHlASP3YgxgjTr5M2AxbbqptxO9aW+g==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-meta": "^2.0.5", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-core-theme-style-helpers/node_modules/@bbva-web-components/bbva-core-meta": {"version": "2.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-meta/-/@bbva-web-components/bbva-core-meta-2.0.5.tgz", "integrity": "sha512-Jp/RT0KGLhjofJ/3yBOuk7ZzfA0+LTS9/INeZMwaUigg5/3jLlRF1WBHi2xSQufoblirNmKdDKw4zlMTmDDv5Q==", "dev": true}, "node_modules/@bbva-web-components/bbva-core-utils": {"version": "1.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-utils/-/@bbva-web-components/bbva-core-utils-1.0.5.tgz", "integrity": "sha512-OoIxeQKWQ31ue3FMpch0O40C7wRBMisAHL+bk1tEyP1EnVx90Z4Z3GckHd3oKQ181Skmp2hSWIO9OGMPKJJxXw==", "dev": true, "dependencies": {"@open-wc/dedupe-mixin": "^1.4.0"}}, "node_modules/@bbva-web-components/bbva-dev-ambient-context": {"version": "1.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-ambient-context/-/@bbva-web-components/bbva-dev-ambient-context-1.1.7.tgz", "integrity": "sha512-/VWJ8jTWvciRq2qees6+GJyXjIPpYPAyitQvUdrZj+X/ALgsADVfYY1+tfGNXI4vxAs9byCuz69etEJJf6aEXg==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-context": "^1.1.7", "@open-wc/dedupe-mixin": "^1.4.0", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-context": {"version": "1.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-context/-/@bbva-web-components/bbva-dev-context-1.1.7.tgz", "integrity": "sha512-x8o8hFc9GLwPaKxWr+Y/fOWLgFxYv2B8DCwtzT0iRt/51QfVVzHkFhmHRJVdXErEv2u2nlAJYf79hLOPVhawkA==", "dev": true, "dependencies": {"@lit/context": "^1.1.2", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-copy-clipboard-button": {"version": "1.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-copy-clipboard-button/-/@bbva-web-components/bbva-dev-copy-clipboard-button-1.1.7.tgz", "integrity": "sha512-F8HMtMBayXShFwUbEa34yP08LtIJxFLbV4NPh7ovVlm8Si5hR6fHoFc+Nd0t649X9MjUUwBnHhbgtz3Om8zBrg==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-ambient-context": "^1.1.7", "@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-ui-assets": "^1.1.7", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-demo-code-editor": {"version": "0.1.40", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-demo-code-editor/-/@bbva-web-components/bbva-dev-demo-code-editor-0.1.40.tgz", "integrity": "sha512-UucNUK2i2CxYkpudxloZAdSJqVMvxwuWSSgHRr79+zFQdi1sGFiMT0xWyabJ/8D54UG+AADyz/rr4rA6FOU0CQ==", "dev": true, "dependencies": {"@codemirror/commands": "^6.6.0", "@codemirror/lang-css": "^6.2.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/language": "^6.10.2", "@codemirror/view": "^6.28.1", "@lezer/highlight": "^1.2.0", "codemirror": "^6.0.1", "lit": "^3.1.4", "prettier": "^3.3.2"}}, "node_modules/@bbva-web-components/bbva-dev-demo-doc-viewer": {"version": "1.1.16", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-demo-doc-viewer/-/@bbva-web-components/bbva-dev-demo-doc-viewer-1.1.16.tgz", "integrity": "sha512-5RHZIej6L+vP8zW41VnJx4sk8QCEJBF8kH+4S6Ze1VIr/7qkiju2sr08iqene57tc7qO2ojtRQC7m9gX/Slfew==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-copy-clipboard-button": "^1.1.7", "@bbva-web-components/bbva-dev-demo-files-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-doc-collapsible": "^1.0.17", "@bbva-web-components/bbva-dev-doc-styling": "^1.0.1", "@bbva-web-components/bbva-dev-doc-tag-label": "^1.1.16", "@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-json-doc-formatter": "^0.1.36", "@bbva-web-components/bbva-dev-json-object-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-markdown-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-prism-formatter": "^1.0.17", "@bbva-web-components/bbva-dev-ui-assets": "^1.1.7", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-demo-files-viewer": {"version": "1.0.17", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-demo-files-viewer/-/@bbva-web-components/bbva-dev-demo-files-viewer-1.0.17.tgz", "integrity": "sha512-xvDLcjFzCddynbWHfcWuzcAcSsojHA90lbMzN3tnrLEzp1saae4Lc9RjWIvm42BBkp0S1MSOfdr7jkXPWYZHEQ==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-doc-collapsible": "^1.0.17", "@bbva-web-components/bbva-dev-doc-tag-label": "^1.1.16", "@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-json-object-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-markdown-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-prism-formatter": "^1.0.17", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-demo-helper": {"version": "1.3.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-demo-helper/-/@bbva-web-components/bbva-dev-demo-helper-1.3.7.tgz", "integrity": "sha512-6TcmD9RbH1PIc8wDANc6sXX51advR7L6dp+WXMwYjsWi/kdg21h5k7LpFIE7AgueAfQ/aHaEyptPtXrwoocjEA==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-context": "^1.1.7", "@bbva-web-components/bbva-dev-copy-clipboard-button": "^1.1.7", "@bbva-web-components/bbva-dev-demo-code-editor": "^0.1.40", "@bbva-web-components/bbva-dev-demo-doc-viewer": "^1.1.16", "@bbva-web-components/bbva-dev-demo-files-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-json-object-viewer": "^1.0.17", "@bbva-web-components/bbva-dev-theme-style-helpers": "^0.1.40", "@bbva-web-components/bbva-dev-ui-assets": "^1.1.7", "@bbva-web-components/bbva-dev-ui-link": "^1.1.7", "@bbva-web-components/bbva-dev-ui-select": "^1.1.7", "@bbva-web-components/bbva-dev-utils": "^0.2.1", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-demo-theme": {"version": "2.1.12", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-demo-theme/-/@bbva-web-components/bbva-dev-demo-theme-2.1.12.tgz", "integrity": "sha512-0E+h4Q3s2ayGUWimyEjG1bgzDzs/JCRZ7QrR9J5kgw3Zj0uEl1mR5I+S6opodg45lQJ9poEWlQxeQncKDeSAGA==", "dev": true, "dependencies": {"@bbva-experience/tokens": "^2.6.0", "@bbva-experience/tokens-spherica": "^1.0.0-rc.3", "@bbva-web-components/bbva-dev-theme-style-helpers": "^0.1.40", "@bbva-web-components/bbva-foundations-design-tokens-utils": "^2.0.0", "@bbva-web-components/bbva-foundations-font-face-theme": "^2.0.0", "@bbva-web-components/bbva-legacy-foundations-font-face-theme": "^1.0.0", "lit": "^3.2.0"}}, "node_modules/@bbva-web-components/bbva-dev-doc-collapsible": {"version": "1.0.17", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-doc-collapsible/-/@bbva-web-components/bbva-dev-doc-collapsible-1.0.17.tgz", "integrity": "sha512-tWTbpJL3IVTTg80Oov2J6arZTEC3EAatNKL3E5CgzTDsCqSK6rcs/snjVLd8XrWwD2JYcvtlMyFqabUJHt4pow==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-ui-assets": "^1.1.7", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-doc-styling": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-doc-styling/-/@bbva-web-components/bbva-dev-doc-styling-1.0.1.tgz", "integrity": "sha512-ylrk8uAhNnMPDSdDRVCXE30QyFvgcXjQ0oNMXyeXlIZgua8DcaVYmvH2KzWV5giUPtxc8oZuR0xojkGOyXuBEg==", "dev": true, "dependencies": {"lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-doc-tag-label": {"version": "1.1.16", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-doc-tag-label/-/@bbva-web-components/bbva-dev-doc-tag-label-1.1.16.tgz", "integrity": "sha512-xtzja1BXFIG1hamo3KV5uzd5eQND8dk5Q8/K20qm377EPWW7wqyBdQw+5Nt9hWRIpS5JEWgn2x/BWv54fPlFXQ==", "dev": true, "dependencies": {"lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-focus-visible-mixin": {"version": "1.0.17", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-focus-visible-mixin/-/@bbva-web-components/bbva-dev-focus-visible-mixin-1.0.17.tgz", "integrity": "sha512-ZE7u69mazMAhfCWFkAFqtdNgQ0oKVNBgn41BPs6X6iX0btUmSaEnoNrVz/IawHM3islUHwPMCXCQV6i4wiGRrg==", "dev": true, "dependencies": {"@open-wc/dedupe-mixin": "^1.4.0"}}, "node_modules/@bbva-web-components/bbva-dev-json-doc-formatter": {"version": "0.1.36", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-json-doc-formatter/-/@bbva-web-components/bbva-dev-json-doc-formatter-0.1.36.tgz", "integrity": "sha512-obukAEXihQd02ey0ZG0dEJWdXVANzuirOKxbgpbTxPafXvYtAIanCZyZIz1lXuU0ZRXUFxr3969ITKZd3LZk0w==", "dev": true}, "node_modules/@bbva-web-components/bbva-dev-json-object-viewer": {"version": "1.0.17", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-json-object-viewer/-/@bbva-web-components/bbva-dev-json-object-viewer-1.0.17.tgz", "integrity": "sha512-Tapv/VQx1I0F58grnWla1B301r88N7E9OyTbC/uQKlbvgo//1H8OF3rQN4Aq/AvHElrN+SqwzS/5u0zNuxIK/Q==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-copy-clipboard-button": "^1.1.7", "json5": "^2.2.3", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-markdown-viewer": {"version": "1.0.17", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-markdown-viewer/-/@bbva-web-components/bbva-dev-markdown-viewer-1.0.17.tgz", "integrity": "sha512-P5GtdHWFpxxSPFcNY690TZPwAdpDQHMk2op2e83J5vUezh5lGK9HuBOmenMCzrp6yzx07S8vzXQ2vCuf19qNzg==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-doc-styling": "^1.0.1", "@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-json-doc-formatter": "^0.1.36", "@bbva-web-components/bbva-dev-prism-formatter": "^1.0.17", "lit": "^3.1.4", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1"}}, "node_modules/@bbva-web-components/bbva-dev-meta": {"version": "0.1.40", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-meta/-/@bbva-web-components/bbva-dev-meta-0.1.40.tgz", "integrity": "sha512-i6ywynHLdH9RuNo8wE3IPYXige71Dyml62/M6RWRKYvojjds1Ifn5JfQxuFOPO9aqy1HgfZ04ErzGvAtdHXCqA==", "dev": true}, "node_modules/@bbva-web-components/bbva-dev-prism-formatter": {"version": "1.0.17", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-prism-formatter/-/@bbva-web-components/bbva-dev-prism-formatter-1.0.17.tgz", "integrity": "sha512-wEgdajhFhSwNu7vESOj/bGTIVqgpthsbDgBnXu2KivaobgLy59gRpvnRPFR3HT0BCXTSEjBwHc8UfPGB1+y8lQ==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-copy-clipboard-button": "^1.1.7", "lit": "^3.1.4", "prismjs": "^1.29.0"}}, "node_modules/@bbva-web-components/bbva-dev-theme-style-helpers": {"version": "0.1.40", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-theme-style-helpers/-/@bbva-web-components/bbva-dev-theme-style-helpers-0.1.40.tgz", "integrity": "sha512-QGgfKFKtN5tR8AdRN8pbsx8P3PJycoYsfV9+ALoc39LLVzd/o5HLNl/2L3Hg4agTKE72RBB67hg/QHaddCIDow==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-meta": "^0.1.40", "lit": "^3.1.4"}}, "node_modules/@bbva-web-components/bbva-dev-ui-assets": {"version": "1.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-ui-assets/-/@bbva-web-components/bbva-dev-ui-assets-1.1.7.tgz", "integrity": "sha512-6Ykg9YZpOD+9C9WKCp73JP5Lx+HJxlzwo0hfqWIi+04qi8WyUQT+3QryT+FeC7LWz3fw/OjK6VKnq3wL/+jRPA==", "dev": true, "dependencies": {"lit": "^3.0.0"}}, "node_modules/@bbva-web-components/bbva-dev-ui-link": {"version": "1.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-ui-link/-/@bbva-web-components/bbva-dev-ui-link-1.1.7.tgz", "integrity": "sha512-/hY3zUkQwzXmauldAMJmhGe4/Eu/fEuXT1eH89bbvdEVLS2tfeFX0e4zJ9DX3/+Gr2qhBiATMDyQib4JtGgm5w==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-ambient-context": "^1.1.7", "lit": "^3.0.0"}}, "node_modules/@bbva-web-components/bbva-dev-ui-select": {"version": "1.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-ui-select/-/@bbva-web-components/bbva-dev-ui-select-1.1.7.tgz", "integrity": "sha512-g9X+qRbB+xpNXdEYS2q42JRKrhn85dfF+0DbweqSLMvLmInMlIfR+fHnDloTbc7syaRKKsNTVnq6g/KrJUIsVQ==", "dev": true, "dependencies": {"@bbva-web-components/bbva-dev-ambient-context": "^1.1.7", "@bbva-web-components/bbva-dev-focus-visible-mixin": "^1.0.17", "@bbva-web-components/bbva-dev-ui-assets": "^1.1.7", "lit": "^3.0.0"}}, "node_modules/@bbva-web-components/bbva-dev-utils": {"version": "0.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-dev-utils/-/@bbva-web-components/bbva-dev-utils-0.2.1.tgz", "integrity": "sha512-/PE1+h1hZJzXX4/UDACWUQYOEPzX1uqHzx0YJNQJ2Zgoc5vsKTLBHDsjmMJiMpgTmQPMYsEhXfhkoctCJbxyXg==", "dev": true, "dependencies": {"@open-wc/dedupe-mixin": "^1.4.0"}}, "node_modules/@bbva-web-components/bbva-foundations-design-tokens-utils": {"version": "2.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-design-tokens-utils/-/@bbva-web-components/bbva-foundations-design-tokens-utils-2.0.1.tgz", "integrity": "sha512-1lrAZTohZQaeDKT/m/PNUPrr3nGnOWtdFxEn+SAZfDQ4jkEfff8z6eRn1s8Mk9c3ygUAkCbf2oscaS7VLU16Gg==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^2.0.0", "lit": "^3.2.0"}}, "node_modules/@bbva-web-components/bbva-foundations-design-tokens-utils/node_modules/@bbva-web-components/bbva-core-lit-helpers": {"version": "2.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-lit-helpers/-/@bbva-web-components/bbva-core-lit-helpers-2.0.5.tgz", "integrity": "sha512-BUBGLGS0AQcv+wbezjQ8IWLdjQYXKkIA550T59l0x3f91KVIb8K4grlauujZndD+/Tc/HjkcM8jkx6DugTWERQ==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-lit-directives": "^1.0.1", "@bbva-web-components/bbva-core-theme-style-helpers": "^1.0.5", "@bbva-web-components/bbva-core-utils": "^1.0.5"}}, "node_modules/@bbva-web-components/bbva-foundations-ellipsis": {"version": "1.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-ellipsis/-/@bbva-web-components/bbva-foundations-ellipsis-1.0.4.tgz", "integrity": "sha512-/WbvhgSH3M7uMeKXtAOynZnBsoy1SOBj+37izkrjEGo9D3gQvmMz5mK0W24sVqq4d+ok8XsXe4fsUpCTeiK2uw==", "dependencies": {"lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-foundations-font-face-theme": {"version": "2.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-font-face-theme/-/@bbva-web-components/bbva-foundations-font-face-theme-2.0.1.tgz", "integrity": "sha512-AkGiYAmJb9OhnFolQEbgbnuhaNh+mSFAgh3X9sqnrYLLuBH4SE9kX3wH7t76ZASI7xk9uYzxoZMWTZhfbtuCTA==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^2.0.0", "lit": "^3.2.0"}}, "node_modules/@bbva-web-components/bbva-foundations-font-face-theme/node_modules/@bbva-web-components/bbva-core-lit-helpers": {"version": "2.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-lit-helpers/-/@bbva-web-components/bbva-core-lit-helpers-2.0.5.tgz", "integrity": "sha512-BUBGLGS0AQcv+wbezjQ8IWLdjQYXKkIA550T59l0x3f91KVIb8K4grlauujZndD+/Tc/HjkcM8jkx6DugTWERQ==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-lit-directives": "^1.0.1", "@bbva-web-components/bbva-core-theme-style-helpers": "^1.0.5", "@bbva-web-components/bbva-core-utils": "^1.0.5"}}, "node_modules/@bbva-web-components/bbva-foundations-grid-utils": {"version": "0.5.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-grid-utils/-/@bbva-web-components/bbva-foundations-grid-utils-0.5.4.tgz", "integrity": "sha512-5Dz7xjwZMAM0seg6obaGKJRjzRF7uI3VzfF89aHNu+cr7wRC5J0YI5HXRf9xkiDbr2PcclnhNTbquFPkwmWRZA==", "dependencies": {"lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-foundations-icons": {"version": "1.25.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-icons/-/@bbva-web-components/bbva-foundations-icons-1.25.1.tgz", "integrity": "sha512-PIaeQpPDcfT+jGHVTefR25ghmiwys1Be+YTx6UG8rOuW7FoF6ifyqqEojhBBv2RyMQwL5gjKlUKETfZebDRzzQ==", "dependencies": {"@bbva-web-components/bbva-core-iconset": "^1.0.0", "@bbva-web-components/bbva-core-lit-helpers": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-foundations-photo-treatments": {"version": "1.0.28", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-photo-treatments/-/@bbva-web-components/bbva-foundations-photo-treatments-1.0.28.tgz", "integrity": "sha512-qWDeia30i6iFVp+V/tnxfgCyXOYJ0X/FhjJFJSYASQ/9MqXsm6x2fEsYFaCqqvhJHV/DgU/kovpCRcTBIhTtxA==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.0.0", "@bbva-web-components/bbva-foundations-styles": "^2.9.8", "@bbva-web-components/bbva-foundations-tokens": "^1.5.1", "lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-foundations-sass": {"version": "1.4.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-sass/-/@bbva-web-components/bbva-foundations-sass-1.4.3.tgz", "integrity": "sha512-scbn7Yt+Vvmpk4UUAPaS0Hz87zI0iQirDJXijS2zQYg9p/9blfj1zjpMEmKXxBqJ7qSxfz2q+ikPSJ7sOhafBA==", "dev": true}, "node_modules/@bbva-web-components/bbva-foundations-styles": {"version": "2.9.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-styles/-/@bbva-web-components/bbva-foundations-styles-2.9.8.tgz", "integrity": "sha512-1ApHt1WcgFJWnHi786n9IKy4sdAhHDC1vOILpae41OzYfyhGJGFUXFGtlzC8/a3yQ79o43Tow7hO9hzGk5DUKw==", "dependencies": {"@bbva-web-components/bbva-foundations-ellipsis": "^1.0.4", "@bbva-web-components/bbva-foundations-grid-utils": "^0.5.4", "@bbva-web-components/bbva-foundations-photo-treatments": "^1.0.28", "@bbva-web-components/bbva-foundations-tokens": "^1.5.1", "@bbva-web-components/bbva-foundations-tools": "^1.3.4", "lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-foundations-tokens": {"version": "1.5.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-tokens/-/@bbva-web-components/bbva-foundations-tokens-1.5.1.tgz", "integrity": "sha512-EJAVr7t9ghYy66dMh5lF7mnJCYSebdP0H38qlWjTArI6fyqzj6AahCFayV5LcjSLdkWe/STWlIsV3qEFoGgvlA=="}, "node_modules/@bbva-web-components/bbva-foundations-tools": {"version": "1.3.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-foundations-tools/-/@bbva-web-components/bbva-foundations-tools-1.3.4.tgz", "integrity": "sha512-DNHoAZzvFRQUHJVrOHlcxgm21PRA22IQrWdcPfuTT5dbhNej53W1xNuJ7ZswDAnlTuA5ZjOuELQj50qffp8VnQ==", "dependencies": {"@bbva-web-components/bbva-foundations-tokens": "^1.5.1", "esm": "^3.2.25", "lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-legacy-foundations-font-face-theme": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-legacy-foundations-font-face-theme/-/@bbva-web-components/bbva-legacy-foundations-font-face-theme-1.1.0.tgz", "integrity": "sha512-U5hbgflMf4x7V4Sgf9haT9/HJIqpmGj/Td3YRkPxuWg6QezrXMFi8ncXfWxbhtCv1kI06FweeNnH8bPb2E/bNg==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^2.0.0", "lit": "^3.2.0"}}, "node_modules/@bbva-web-components/bbva-legacy-foundations-font-face-theme/node_modules/@bbva-web-components/bbva-core-lit-helpers": {"version": "2.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-core-lit-helpers/-/@bbva-web-components/bbva-core-lit-helpers-2.0.5.tgz", "integrity": "sha512-BUBGLGS0AQcv+wbezjQ8IWLdjQYXKkIA550T59l0x3f91KVIb8K4grlauujZndD+/Tc/HjkcM8jkx6DugTWERQ==", "dev": true, "dependencies": {"@bbva-web-components/bbva-core-lit-directives": "^1.0.1", "@bbva-web-components/bbva-core-theme-style-helpers": "^1.0.5", "@bbva-web-components/bbva-core-utils": "^1.0.5"}}, "node_modules/@bbva-web-components/bbva-web-amount": {"version": "1.5.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-web-amount/-/@bbva-web-components/bbva-web-amount-1.5.0.tgz", "integrity": "sha512-k44JTok7Gl4glJnmwAJ0J5jIfF/V9QZD4YEprugGKHGzUp91DvQgfi/QzPkfYxnoMkqyYCR744obl5Y8VS+Uow==", "dependencies": {"@bbva-web-components/bbva-core-amount-currency-mixin": "^1.0.0", "@bbva-web-components/bbva-core-amount-format-mixin": "^1.0.0", "@bbva-web-components/bbva-core-lit-helpers": "^1.0.0", "@bbva-web-components/bbva-foundations-styles": "^2.0.0", "lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-web-link": {"version": "1.9.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-web-link/-/@bbva-web-components/bbva-web-link-1.9.0.tgz", "integrity": "sha512-uQTjRbAAMxc0yos6GAd4BIRKZneB9nSsI7FCkT+kVlFhojXF+mF7jJ5OgZuNXUwfeUsgjEz8sVOInnUF4fso/g==", "dependencies": {"@bbva-web-components/bbva-core-action": "^3.0.0", "@bbva-web-components/bbva-core-icon": "^1.0.0", "@bbva-web-components/bbva-core-lit-helpers": "^1.0.0", "@bbva-web-components/bbva-foundations-styles": "^2.0.0", "@open-wc/lit-helpers": "^0.3.0", "@open-wc/scoped-elements": "^1.0.0", "lit-element": "^2.0.0"}}, "node_modules/@bbva-web-components/bbva-web-link/node_modules/@open-wc/scoped-elements": {"version": "1.3.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/scoped-elements/-/scoped-elements-1.3.7.tgz", "integrity": "sha512-q/wKf4sXl7cr1kNfl8z6TLO2TrpXsFMCrfCD51sCEljltwYIXOmI6SnRXmWlnzG37A8AwHRpDXYmjPj2F4gPxA==", "license": "MIT", "dependencies": {"@open-wc/dedupe-mixin": "^1.3.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-web-navigation-contextual-box": {"version": "1.7.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-web-navigation-contextual-box/-/@bbva-web-components/bbva-web-navigation-contextual-box-1.7.0.tgz", "integrity": "sha512-vLDlS25/SgB/5cTOAwcFnLfRyl9bQEMRxcY1k1CQ8iZTFU3sm51rIN8qdT9DVF2SAPdJ5SpXp+McxOSsMhsG0Q==", "dependencies": {"@bbva-web-components/bbva-core-focus-visible-mixin": "^1.0.0", "@bbva-web-components/bbva-core-lit-helpers": "^1.0.0", "@bbva-web-components/bbva-foundations-styles": "^2.0.0", "@bbva-web-components/bbva-web-link": "^1.9.0", "@open-wc/scoped-elements": "^1.0.0", "lit-element": "^2.5.1"}}, "node_modules/@bbva-web-components/bbva-web-navigation-contextual-box/node_modules/@open-wc/scoped-elements": {"version": "1.3.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/scoped-elements/-/scoped-elements-1.3.7.tgz", "integrity": "sha512-q/wKf4sXl7cr1kNfl8z6TLO2TrpXsFMCrfCD51sCEljltwYIXOmI6SnRXmWlnzG37A8AwHRpDXYmjPj2F4gPxA==", "license": "MIT", "dependencies": {"@open-wc/dedupe-mixin": "^1.3.0", "lit-html": "^1.0.0"}}, "node_modules/@bbva-web-components/bbva-web-table": {"version": "1.6.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@bbva-web-components/bbva-web-table/-/@bbva-web-components/bbva-web-table-1.6.0.tgz", "integrity": "sha512-RH+FlE3szUOUVyRaQNdWp5+UtYFa5XWM4j0mcZik8ZeqbP/Yu/Z1La4ooIbxSEvFyHUpBnKEI2NHRq89cc3Ogg==", "dependencies": {"@bbva-web-components/bbva-core-lit-helpers": "^1.0.0", "@bbva-web-components/bbva-foundations-styles": "^2.0.0", "lit-element": "^2.0.0"}}, "node_modules/@codemirror/autocomplete": {"version": "6.18.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/autocomplete/-/autocomplete-6.18.6.tgz", "integrity": "sha512-PHHBXFomUs5DF+9tCOM/UoW6XQ4R44lLNNhRaW9PKPTU0D7lIjRg3ElxaJnTwsl/oHiR93WSXDBrekhoUGCPtg==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0"}}, "node_modules/@codemirror/commands": {"version": "6.8.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/commands/-/commands-6.8.1.tgz", "integrity": "sha512-KlGVYufHMQzxbdQONiLyGQDUW0itrLZwq3CcY7xpv9ZLRHqzkBSoteocBHtMCoY7/Ci4xhzSrToIeLg7FxHuaw==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/language": "^6.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.27.0", "@lezer/common": "^1.1.0"}}, "node_modules/@codemirror/lang-css": {"version": "6.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/lang-css/-/lang-css-6.3.1.tgz", "integrity": "sha512-kr5fwBGiGtmz6l0LSJIbno9QrifNMUusivHbnA1H6Dmqy4HZFte3UAICix1VuKo0lMPKQr2rqB+0BkKi/S3Ejg==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/state": "^6.0.0", "@lezer/common": "^1.0.2", "@lezer/css": "^1.1.7"}}, "node_modules/@codemirror/lang-html": {"version": "6.4.9", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/lang-html/-/lang-html-6.4.9.tgz", "integrity": "sha512-aQv37pIMSlueybId/2PVSP6NPnmurFDVmZwzc7jszd2KAF8qd4VBbvNYPXWQq90WIARjsdVkPbw29pszmHws3Q==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/lang-css": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/language": "^6.4.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0", "@lezer/css": "^1.1.0", "@lezer/html": "^1.3.0"}}, "node_modules/@codemirror/lang-javascript": {"version": "6.2.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/lang-javascript/-/lang-javascript-6.2.4.tgz", "integrity": "sha512-0WVmhp1QOqZ4Rt6GlVGwKJN3KW7Xh4H2q8ZZNGZaP6lRdxXJzmjm4FqvmOojVj6khWJHIb9sp7U/72W7xQgqAA==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/language": "^6.6.0", "@codemirror/lint": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.17.0", "@lezer/common": "^1.0.0", "@lezer/javascript": "^1.0.0"}}, "node_modules/@codemirror/language": {"version": "6.11.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/language/-/language-6.11.2.tgz", "integrity": "sha512-p44TsNArL4IVXDTbapUmEkAlvWs2CFQbcfc0ymDsis1kH2wh0gcY96AS29c/vp2d0y2Tquk1EDSaawpzilUiAw==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@lezer/common": "^1.1.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0", "style-mod": "^4.0.0"}}, "node_modules/@codemirror/lint": {"version": "6.8.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/lint/-/lint-6.8.5.tgz", "integrity": "sha512-s3n3KisH7dx3vsoeGMxsbRAgKe4O1vbrnKBClm99PU0fWxmxsx5rR2PfqQgIt+2MMJBHbiJ5rfIdLYfB9NNvsA==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.35.0", "crelt": "^1.0.5"}}, "node_modules/@codemirror/search": {"version": "6.5.11", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/search/-/search-6.5.11.tgz", "integrity": "sha512-KmWepDE6jUdL6n8cAAqIpRmLPBZ5ZKnicE8oGU/s3QrAVID+0VhLFrzUucVKHG5035/BSykhExDL/Xm7dHthiA==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "crelt": "^1.0.5"}}, "node_modules/@codemirror/state": {"version": "6.5.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/state/-/state-6.5.2.tgz", "integrity": "sha512-FVqsPqtPWKVVL3dPSxy8wEF/ymIEuVzF1PK3VbUgrxXpJUSHQWWZz4JMToquRxnkw+36LTamCZG2iua2Ptq0fA==", "dev": true, "license": "MIT", "dependencies": {"@marijn/find-cluster-break": "^1.0.0"}}, "node_modules/@codemirror/view": {"version": "6.38.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@codemirror/view/-/view-6.38.1.tgz", "integrity": "sha512-RmTOkE7hRU3OVREqFVITWHz6ocgBjv08GoePscAakgVQfciA3SGCEk7mb9IzwW61cKKmlTpHXG6DUE5Ubx+MGQ==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/state": "^6.5.0", "crelt": "^1.0.6", "style-mod": "^4.1.0", "w3c-keyname": "^2.2.4"}}, "node_modules/@esm-bundle/chai": {"version": "4.3.4-fix.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@esm-bundle/chai/-/chai-4.3.4-fix.0.tgz", "integrity": "sha512-26SKdM4uvDWlY8/OOOxSB1AqQWeBosCX3wRYUZO7enTAj03CtVxIiCimYVG2WpULcyV51qapK4qTovwkUr5Mlw==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^4.2.12"}}, "node_modules/@formatjs/intl-unified-numberformat": {"version": "3.3.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@formatjs/intl-unified-numberformat/-/intl-unified-numberformat-3.3.7.tgz", "integrity": "sha512-KnWgLRHzCAgT9eyt3OS34RHoyD7dPDYhRcuKn+/6Kv2knDF8Im43J6vlSW6Hm1w63fNq3ZIT1cFk7RuVO3Psag==", "deprecated": "We have renamed the package to @formatjs/intl-numberformat", "license": "MIT", "dependencies": {"@formatjs/intl-utils": "^2.3.0"}}, "node_modules/@formatjs/intl-utils": {"version": "2.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@formatjs/intl-utils/-/intl-utils-2.3.0.tgz", "integrity": "sha512-KWk80UPIzPmUg+P0rKh6TqspRw0G6eux1PuJr+zz47ftMaZ9QDwbGzHZbtzWkl5hgayM/qrKRutllRC7D/vVXQ==", "deprecated": "the package is rather renamed to @formatjs/ecma-abstract with some changes in functionality (primarily selectUnit is removed and we don't plan to make any further changes to this package", "license": "MIT"}, "node_modules/@hapi/bourne": {"version": "3.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@hapi/bourne/-/bourne-3.0.0.tgz", "integrity": "sha512-Waj1cwPXJDucOib4a3bAISsKJVb15MKi9IvmTI/7ssVEm6sywXGjVJDhl6/umt1pK1ZS7PacXU3A1PmFKHEZ2w==", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@lezer/common": {"version": "1.2.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lezer/common/-/common-1.2.3.tgz", "integrity": "sha512-w7ojc8ejBqr2REPsWxJjrMFsA/ysDCFICn8zEOR9mrqzOu2amhITYuLD8ag6XZf0CFXDrhKqw7+tW8cX66NaDA==", "dev": true, "license": "MIT"}, "node_modules/@lezer/css": {"version": "1.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lezer/css/-/css-1.3.0.tgz", "integrity": "sha512-pBL7hup88KbI7hXnZV3PQsn43DHy6TWyzuyk2AO9UyoXcDltvIdqWKE1dLL/45JVZ+YZkHe1WVHqO6wugZZWcw==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.2.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.3.0"}}, "node_modules/@lezer/highlight": {"version": "1.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lezer/highlight/-/highlight-1.2.1.tgz", "integrity": "sha512-Z5duk4RN/3zuVO7Jq0pGLJ3qynpxUVsh7IbUbGj88+uV2ApSAn6kWg2au3iJb+0Zi7kKtqffIESgNcRXWZWmSA==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.0.0"}}, "node_modules/@lezer/html": {"version": "1.3.10", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lezer/html/-/html-1.3.10.tgz", "integrity": "sha512-dqpT8nISx/p9Do3AchvYGV3qYc4/rKr3IBZxlHmpIKam56P47RSHkSF5f13Vu9hebS1jM0HmtJIwLbWz1VIY6w==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.2.0", "@lezer/highlight": "^1.0.0", "@lezer/lr": "^1.0.0"}}, "node_modules/@lezer/javascript": {"version": "1.5.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lezer/javascript/-/javascript-1.5.1.tgz", "integrity": "sha512-ATOImjeVJuvgm3JQ/bpo2Tmv55HSScE2MTPnKRMRIPx2cLhHGyX2VnqpHhtIV1tVzIjZDbcWQm+NCTF40ggZVw==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.2.0", "@lezer/highlight": "^1.1.3", "@lezer/lr": "^1.3.0"}}, "node_modules/@lezer/lr": {"version": "1.4.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lezer/lr/-/lr-1.4.2.tgz", "integrity": "sha512-pu0K1jCIdnQ12aWNaAVU5bzi7Bd1w54J3ECgANPmYLtQKP0HBj2cE/5coBD66MT10xbtIuUr7tg0Shbsvk0mDA==", "dev": true, "license": "MIT", "dependencies": {"@lezer/common": "^1.0.0"}}, "node_modules/@lit-labs/ssr-dom-shim": {"version": "1.4.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lit-labs/ssr-dom-shim/-/ssr-dom-shim-1.4.0.tgz", "integrity": "sha512-ficsEARKnmmW5njugNYKipTm4SFnbik7CXtoencDZzmzo/dQ+2Q0bgkzJuoJP20Aj0F+izzJjOqsnkd6F/o1bw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@lit/context": {"version": "1.1.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lit/context/-/context-1.1.6.tgz", "integrity": "sha512-M26qDE6UkQbZA2mQ3RjJ3Gzd8TxP+/0obMgE5HfkfLhEEyYE3Bui4A5XHiGPjy0MUGAyxB3QgVuw2ciS0kHn6A==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@lit/reactive-element": "^1.6.2 || ^2.1.0"}}, "node_modules/@lit/reactive-element": {"version": "2.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@lit/reactive-element/-/reactive-element-2.1.1.tgz", "integrity": "sha512-N+dm5PAYdQ8e6UlywyyrgI2t++wFGXfHx+dSJ1oBrg6FAxUj40jId++EaRm80MKX5JnlH1sBsyZ5h0bcZKemCg==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@lit-labs/ssr-dom-shim": "^1.4.0"}}, "node_modules/@marijn/find-cluster-break": {"version": "1.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@marijn/find-cluster-break/-/find-cluster-break-1.0.2.tgz", "integrity": "sha512-l0h88YhZFyKdXIFNfSWpyjStDjGHwZ/U7iobcK1cQQD8sejsONdQtTVU+1wVN1PBw40PiiHB1vA5S7VTfQiP9g==", "dev": true, "license": "MIT"}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@open-wc/dedupe-mixin": {"version": "1.4.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/dedupe-mixin/-/dedupe-mixin-1.4.0.tgz", "integrity": "sha512-Sj7gKl1TLcDbF7B6KUhtvr+1UCxdhMbNY5KxdU5IfMFWqL8oy1ZeAcCANjoB1TL0AJTcPmcCFsCbHf8X2jGDUA==", "license": "MIT"}, "node_modules/@open-wc/lit-helpers": {"version": "0.3.12", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/lit-helpers/-/lit-helpers-0.3.12.tgz", "integrity": "sha512-ALnAkuHxYvMZNWnujsAd1rlURSjwHZazvUufZp+fKOybzwjPNH2NAQlIRQpe/Z0r7bJCLwV9h+Zy6iOL85VXlw==", "license": "MIT", "peerDependencies": {"lit-element": "^2.0.0", "lit-html": "^1.0.0"}}, "node_modules/@open-wc/scoped-elements": {"version": "2.2.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/scoped-elements/-/scoped-elements-2.2.4.tgz", "integrity": "sha512-12X4F4QGPWcvPbxAiJ4v8wQFCOu+laZHRGfTrkoj+3JzACCtuxHG49YbuqVzQ135QPKCuhP9wA0kpGGEfUegyg==", "dev": true, "license": "MIT", "dependencies": {"@lit/reactive-element": "^1.0.0 || ^2.0.0", "@open-wc/dedupe-mixin": "^1.4.0"}}, "node_modules/@open-wc/semantic-dom-diff": {"version": "0.20.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/semantic-dom-diff/-/semantic-dom-diff-0.20.1.tgz", "integrity": "sha512-mPF/RPT2TU7Dw41LEDdaeP6eyTOWBD4z0+AHP4/d0SbgcfJZVRymlIB6DQmtz0fd2CImIS9kszaMmwMt92HBPA==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^4.3.1", "@web/test-runner-commands": "^0.9.0"}}, "node_modules/@open-wc/testing": {"version": "3.2.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/testing/-/testing-3.2.2.tgz", "integrity": "sha512-byN4dJTd6ZyI9mWmI4lVj30uiu+rYvQr93g64Pd7UFBdAUgb02DHLj6fkJ1gjxA6LC/MeFd7K7mOZ4+vKrMptw==", "dev": true, "license": "MIT", "dependencies": {"@esm-bundle/chai": "^4.3.4-fix.0", "@open-wc/semantic-dom-diff": "^0.20.0", "@open-wc/testing-helpers": "^2.3.1", "@types/chai-dom": "^1.11.0", "@types/sinon-chai": "^3.2.3", "chai-a11y-axe": "^1.5.0"}}, "node_modules/@open-wc/testing-helpers": {"version": "2.3.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@open-wc/testing-helpers/-/testing-helpers-2.3.2.tgz", "integrity": "sha512-uZMGC/C1m5EiwQsff6KMmCW25TYMQlJt4ilAWIjnelWGFg9HPUiLnlFvAas3ESUP+4OXLO8Oft7p4mHvbYvAEQ==", "dev": true, "license": "MIT", "dependencies": {"@open-wc/scoped-elements": "^2.2.4", "lit": "^2.0.0 || ^3.0.0", "lit-html": "^2.0.0 || ^3.0.0"}}, "node_modules/@open-wc/testing-helpers/node_modules/lit-html": {"version": "3.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lit-html/-/lit-html-3.3.1.tgz", "integrity": "sha512-S9hbyDu/vs1qNrithiNyeyv64c9yqiW9l+DBgI18fL+MTvOtWoFR0FWiyq1TxaYef5wNlpEmzlXoBlZEO+WjoA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@types/trusted-types": "^2.0.2"}}, "node_modules/@types/accepts": {"version": "1.3.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/accepts/-/accepts-1.3.7.tgz", "integrity": "sha512-Pay9fq2lM2wXPWbteBsRAGiWH2hig4ZE2asK+mm7kUzlxRTfL961rj89I6zV/E3PcIkDqyuBEcMxFT7rccugeQ==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/babel__code-frame": {"version": "7.0.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/babel__code-frame/-/babel__code-frame-7.0.6.tgz", "integrity": "sha512-Anitqkl3+KrzcW2k77lRlg/GfLZLWXBuNgbEcIOU6M92yw42vsd3xV/Z/yAHEj8m+KUjL6bWOVOFqX8PFPJ4LA==", "dev": true, "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/body-parser/-/body-parser-1.19.6.tgz", "integrity": "sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/chai": {"version": "4.3.20", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/chai/-/chai-4.3.20.tgz", "integrity": "sha512-/pC9HAB5I/xMlc5FP77qjCnI16ChlJfW0tGa0IUcFn38VJrTV6DeZ60NU5KZBtaOZqjdpwTWohz5HU1RrhiYxQ==", "dev": true, "license": "MIT"}, "node_modules/@types/chai-dom": {"version": "1.11.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/chai-dom/-/chai-dom-1.11.3.tgz", "integrity": "sha512-EUEZI7uID4ewzxnU7DJXtyvykhQuwe+etJ1wwOiJyQRTH/ifMWKX+ghiXkxCUvNJ6IQDodf0JXhuP6zZcy2qXQ==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "*"}}, "node_modules/@types/co-body": {"version": "6.1.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/co-body/-/co-body-6.1.3.tgz", "integrity": "sha512-Uh<PERSON>rQ5hclX6UJctv5m4Rfp52AfG9o9+d9/HwjxhVB5NjXxr5t9oKgJxN8xRHgr35oo8meUEHUPFWiKg6y71aA==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/content-disposition": {"version": "0.5.9", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/content-disposition/-/content-disposition-0.5.9.tgz", "integrity": "sha512-8uYXI3Gw35MhiVYhG3s295oihrxRyytcRHjSjqnqZVDDy/xcGBRny7+Xj1Wgfhv5QzRtN2hB2dVRBUX9XW3UcQ==", "dev": true, "license": "MIT"}, "node_modules/@types/convert-source-map": {"version": "2.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/convert-source-map/-/convert-source-map-2.0.3.tgz", "integrity": "sha512-ag0BfJLZf6CQz8VIuRIEYQ5Ggwk/82uvTQf27RcpyDNbY0Vw49LIPqAxk5tqYfrCs9xDaIMvl4aj7ZopnYL8bA==", "dev": true, "license": "MIT"}, "node_modules/@types/cookies": {"version": "0.9.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/cookies/-/cookies-0.9.1.tgz", "integrity": "sha512-E/DPgzifH4sM1UMadJMWd6mO2jOd4g1Ejwzx8/uRCDpJis1IrlyQEcGAYEomtAqRYmD5ORbNXMeI9U0RiVGZbg==", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}}, "node_modules/@types/debounce": {"version": "1.2.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/debounce/-/debounce-1.2.4.tgz", "integrity": "sha512-jBqiORIzKDOToaF63Fm//haOCHuwQuLa2202RK4MozpA6lh93eCBc+/8+wZn5OzjJt3ySdc+74SXWXB55Ewtyw==", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "5.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/express/-/express-5.0.3.tgz", "integrity": "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz", "integrity": "sha512-R+33OsgWw7rOhD1emjU7dzCDHucJrgJXMA5PYCzJxVil0dsyx5iBEPHqpPfiKNJQb7lZ1vxwoLR4Z87bBUpeGQ==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-assert": {"version": "1.5.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/http-assert/-/http-assert-1.5.6.tgz", "integrity": "sha512-TTEwmtjgVbYAzZYWyeHPrrtWnfVkm8tQkP8P21uQifPgMRgjrow3XDEYqucuC8SKZJT7pUnhU/JymvjggxO9vw==", "dev": true, "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/http-errors/-/http-errors-2.0.5.tgz", "integrity": "sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "integrity": "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "integrity": "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/keygrip": {"version": "1.0.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/keygrip/-/keygrip-1.0.6.tgz", "integrity": "sha512-l<PERSON>uNAY9xeJt7Bx4t4dx0rYCDqGPW8RXhQZK1td7d4H6E9zYbLoOtjBvfwdTKpsyxQI/2jv+armjX/RW+ZNpXOQ==", "dev": true, "license": "MIT"}, "node_modules/@types/koa": {"version": "2.15.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/koa/-/koa-2.15.0.tgz", "integrity": "sha512-7QFsywoE5URbuVnG3loe03QXuGajrnotr3gQkXcEBShORai23MePfFYdhz90FEtBBpkyIYQbVD+evKtloCgX3g==", "dev": true, "license": "MIT", "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/http-errors": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}}, "node_modules/@types/koa-compose": {"version": "3.2.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/koa-compose/-/koa-compose-3.2.8.tgz", "integrity": "sha512-4Olc63RY+MKvxMwVknCUDhRQX1pFQoBZ/lXcRLP69PQkEpze/0cr8LNqJQe5NFb/b19DWi2a5bTi2VAlQzhJuA==", "dev": true, "license": "MIT", "dependencies": {"@types/koa": "*"}}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "24.0.15", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/node/-/node-24.0.15.tgz", "integrity": "sha512-oaeTSbCef7U/z7rDeJA138xpG3NuKc64/rZ2qmUFkFJmnMsAPaluIifqyWd8hSSMxyP9oie3dLAqYPblag9KgA==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~7.8.0"}}, "node_modules/@types/parse5": {"version": "6.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/parse5/-/parse5-6.0.3.tgz", "integrity": "sha512-SuT16Q1K51EAVPz1K29DJ/sXjhSQ0zjvsypYJ6tlwVsRV9jwW5Adq2ch8Dq8kDBCkYnELS7N7VNCSB5nC56t/g==", "dev": true, "license": "MIT"}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/send/-/send-0.17.5.tgz", "integrity": "sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/serve-static/-/serve-static-1.15.8.tgz", "integrity": "sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/sinon": {"version": "17.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/sinon/-/sinon-17.0.4.tgz", "integrity": "sha512-RHnIrhfPO3+tJT0s7cFaXGZvsL4bbR3/k7z3P312qMS4JaS2Tk+KiwiLx1S0rQ56ERj00u1/BtdyVd0FY+Pdew==", "dev": true, "license": "MIT", "dependencies": {"@types/sinonjs__fake-timers": "*"}}, "node_modules/@types/sinon-chai": {"version": "3.2.12", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/sinon-chai/-/sinon-chai-3.2.12.tgz", "integrity": "sha512-9y0Gflk3b0+NhQZ/oxGtaAJDvRywCa5sIyaVnounqLvmf93yBF4EgIRspePtkMs3Tr844nCclYMlcCNmLCvjuQ==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "*", "@types/sinon": "*"}}, "node_modules/@types/sinonjs__fake-timers": {"version": "8.1.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/sinonjs__fake-timers/-/sinonjs__fake-timers-8.1.5.tgz", "integrity": "sha512-mQkU2jY8jJEF7YHjHvsQO8+3ughTL1mcnn96igfhONmR+fUPSKIkefQYpSe8bsly2Ep7oQbn/6VG5/9/0qcArQ==", "dev": true, "license": "MIT"}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "dev": true, "license": "MIT"}, "node_modules/@types/ws": {"version": "7.4.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@types/ws/-/ws-7.4.7.tgz", "integrity": "sha512-JQbbmxZTZehdc2iszGKs5oC3NFnjeay7mtAWrdt7qNtAVK0g19muApzAy4bm9byz79xa2ZnO/BOBC2R8RC5Lww==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@web/browser-logs": {"version": "0.4.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@web/browser-logs/-/browser-logs-0.4.1.tgz", "integrity": "sha512-ypmMG+72ERm+LvP+loj9A64MTXvWMXHUOu773cPO4L1SV/VWg6xA9Pv7vkvkXQX+ItJtCJt+KQ+U6ui2HhSFUw==", "dev": true, "license": "MIT", "dependencies": {"errorstacks": "^2.4.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@web/dev-server-core": {"version": "0.7.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@web/dev-server-core/-/dev-server-core-0.7.5.tgz", "integrity": "sha512-Da65zsiN6iZPMRuj4Oa6YPwvsmZmo5gtPWhW2lx3GTUf5CAEapjVpZVlUXnKPL7M7zRuk72jSsIl8lo+XpTCtw==", "dev": true, "license": "MIT", "dependencies": {"@types/koa": "^2.11.6", "@types/ws": "^7.4.0", "@web/parse5-utils": "^2.1.0", "chokidar": "^4.0.1", "clone": "^2.1.2", "es-module-lexer": "^1.0.0", "get-stream": "^6.0.0", "is-stream": "^2.0.0", "isbinaryfile": "^5.0.0", "koa": "^2.13.0", "koa-etag": "^4.0.0", "koa-send": "^5.0.1", "koa-static": "^5.0.0", "lru-cache": "^8.0.4", "mime-types": "^2.1.27", "parse5": "^6.0.1", "picomatch": "^2.2.2", "ws": "^7.5.10"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@web/parse5-utils": {"version": "2.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@web/parse5-utils/-/parse5-utils-2.1.0.tgz", "integrity": "sha512-GzfK5disEJ6wEjoPwx8AVNwUe9gYIiwc+x//QYxYDAFKUp4Xb1OJAGLc2l2gVrSQmtPGLKrTRcW90Hv4pEq1qA==", "dev": true, "license": "MIT", "dependencies": {"@types/parse5": "^6.0.1", "parse5": "^6.0.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@web/test-runner-commands": {"version": "0.9.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@web/test-runner-commands/-/test-runner-commands-0.9.0.tgz", "integrity": "sha512-zeLI6QdH0jzzJMDV5O42Pd8WLJtYqovgdt0JdytgHc0d1EpzXDsc7NTCJSImboc2NcayIsWAvvGGeRF69SMMYg==", "dev": true, "license": "MIT", "dependencies": {"@web/test-runner-core": "^0.13.0", "mkdirp": "^1.0.4"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@web/test-runner-core": {"version": "0.13.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@web/test-runner-core/-/test-runner-core-0.13.4.tgz", "integrity": "sha512-84E1025aUSjvZU1j17eCTwV7m5Zg3cZHErV3+CaJM9JPCesZwLraIa0ONIQ9w4KLgcDgJFw9UnJ0LbFf42h6tg==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.11", "@types/babel__code-frame": "^7.0.2", "@types/co-body": "^6.1.0", "@types/convert-source-map": "^2.0.0", "@types/debounce": "^1.2.0", "@types/istanbul-lib-coverage": "^2.0.3", "@types/istanbul-reports": "^3.0.0", "@web/browser-logs": "^0.4.0", "@web/dev-server-core": "^0.7.3", "chokidar": "^4.0.1", "cli-cursor": "^3.1.0", "co-body": "^6.1.0", "convert-source-map": "^2.0.0", "debounce": "^1.2.0", "dependency-graph": "^0.11.0", "globby": "^11.0.1", "internal-ip": "^6.2.0", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.1", "istanbul-reports": "^3.0.2", "log-update": "^4.0.0", "nanocolors": "^0.2.1", "nanoid": "^3.1.25", "open": "^8.0.2", "picomatch": "^2.2.2", "source-map": "^0.7.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@webcomponents/shadycss": {"version": "1.11.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/@webcomponents/shadycss/-/shadycss-1.11.2.tgz", "integrity": "sha512-vRq+GniJAYSBmTRnhCYPAPq6THYqovJ/gzGThWbgEZUQaBccndGTi1hdiUP15HzEco0I6t4RCtXyX0rsSmwgPw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/accepts/-/accepts-1.3.8.tgz", "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "dev": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true, "license": "Python-2.0"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/array-union/-/array-union-2.1.0.tgz", "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/astral-regex": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/astral-regex/-/astral-regex-2.0.0.tgz", "integrity": "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/axe-core": {"version": "4.10.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/axe-core/-/axe-core-4.10.3.tgz", "integrity": "sha512-Xm7bpRXnDSX2YE2YFfBk2FnF0ep6tmG7xPh8iHee8MIcrgq762Nkce856dYtJYLkuIoYZvGfTs/PbZhideTcEg==", "dev": true, "license": "MPL-2.0", "engines": {"node": ">=4"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cache-content-type": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/cache-content-type/-/cache-content-type-1.0.1.tgz", "integrity": "sha512-IKufZ1o4Ut42YUrZSo8+qnMTrFuKkvyoLXUywKz9GJ5BrhOFGhLdkx9sG4KAnVvbY6kEcSFjLQul+DVmBm2bgA==", "dev": true, "license": "MIT", "dependencies": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/cells-mobile-plugins": {"version": "1.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/cells-mobile-plugins/-/cells-mobile-plugins-1.1.1.tgz", "integrity": "sha1-IO4nreEY0AxDRch6TxpRy9kbVdo=", "license": "ISC"}, "node_modules/chai-a11y-axe": {"version": "1.5.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/chai-a11y-axe/-/chai-a11y-axe-1.5.0.tgz", "integrity": "sha512-V/Vg/zJDr9aIkaHJ2KQu7lGTQQm5ZOH4u1k5iTMvIXuSVlSuUo0jcSpSqf9wUn9zl6oQXa4e4E0cqH18KOgKlQ==", "dev": true, "license": "MIT", "dependencies": {"axe-core": "^4.3.3"}}, "node_modules/chokidar": {"version": "4.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/chokidar/-/chokidar-4.0.3.tgz", "integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "dev": true, "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/cli-cursor": {"version": "3.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/cli-cursor/-/cli-cursor-3.1.0.tgz", "integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/clone": {"version": "2.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/clone/-/clone-2.1.2.tgz", "integrity": "sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/co": {"version": "4.6.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/co/-/co-4.6.0.tgz", "integrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==", "dev": true, "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/co-body": {"version": "6.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/co-body/-/co-body-6.2.0.tgz", "integrity": "sha512-Kbpv2Yd1NdL1V/V4cwLVxraHDV6K8ayohr2rmH0J87Er8+zJjcTa6dAn9QMPC9CRgU8+aNajKbSf1TzDB1yKPA==", "dev": true, "license": "MIT", "dependencies": {"@hapi/bourne": "^3.0.0", "inflation": "^2.0.0", "qs": "^6.5.2", "raw-body": "^2.3.3", "type-is": "^1.6.16"}, "engines": {"node": ">=8.0.0"}}, "node_modules/codemirror": {"version": "6.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/codemirror/-/codemirror-6.0.2.tgz", "integrity": "sha512-VhydHotNW5w1UGK0Qj96BwSk/Zqbp9WbnyK2W/eVMv4QyF41INRGpjUhFJY7/uDNuudSc33a/PKr4iDqRduvHw==", "dev": true, "license": "MIT", "dependencies": {"@codemirror/autocomplete": "^6.0.0", "@codemirror/commands": "^6.0.0", "@codemirror/language": "^6.0.0", "@codemirror/lint": "^6.0.0", "@codemirror/search": "^6.0.0", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "license": "MIT"}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true, "license": "MIT"}, "node_modules/cookies": {"version": "0.9.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/cookies/-/cookies-0.9.1.tgz", "integrity": "sha512-TG2hpqe4ELx54QER/S3HQ9SRVnQnGBtKUz5bLQWtYAQ+o6GpgMs6sYUvaiJjVxb+UXwhRhAEP3m7LbsIZ77Hmw==", "dev": true, "license": "MIT", "dependencies": {"depd": "~2.0.0", "keygrip": "~1.1.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/crelt": {"version": "1.0.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/crelt/-/crelt-1.0.6.tgz", "integrity": "sha512-VQ2MBenTq1fWZUH9DJNGti7kKv6EeAuYr3cLwxUWhIu1baTaXh4Ib5W2CqHVqib4/MqbYGJqiL3Zb8GJZr3l4g==", "dev": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/debounce": {"version": "1.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/debounce/-/debounce-1.2.1.tgz", "integrity": "sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-equal": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/deep-equal/-/deep-equal-1.0.1.tgz", "integrity": "sha512-bHtC0iYvWhyaTzvV3CZgPeZQqCOBGyGsVV7v4eevpdkLHfiSrXUdBG+qAuSz4RI70sszvjQ1QSZ98An1yNwpSw==", "dev": true, "license": "MIT"}, "node_modules/default-gateway": {"version": "6.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/default-gateway/-/default-gateway-6.0.3.tgz", "integrity": "sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/delegates/-/delegates-1.0.0.tgz", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "dev": true, "license": "MIT"}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dependency-graph": {"version": "0.11.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/dependency-graph/-/dependency-graph-0.11.0.tgz", "integrity": "sha512-JeMq7fEshyepOWDfcfHK06N3MhyPhz++vtqWhMT5O9A3K42rdsEDpfdVqjaqaAhsw6a+ZqeDvQVtD0hFHQWrzg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "dev": true, "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true, "license": "MIT"}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/entities": {"version": "4.5.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/entities/-/entities-4.5.0.tgz", "integrity": "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/errorstacks": {"version": "2.4.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/errorstacks/-/errorstacks-2.4.1.tgz", "integrity": "sha512-jE4i0SMYevwu/xxAuzhly/KTwtj0xDhbzB6m1xPImxTkw8wcCbgarOQPfCVMi5JKVyW7in29pNJCCJrry3Ynnw==", "dev": true, "license": "MIT"}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "integrity": "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==", "dev": true, "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "dev": true, "license": "MIT"}, "node_modules/esm": {"version": "3.2.25", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/esm/-/esm-3.2.25.tgz", "integrity": "sha512-U1suiZ2oDVWv4zPO56S0NcR5QriEahGtdN2OR6FiOG4WJvcjBVFB0qI4+eKoWFH483PKGuLuu6V8Z4T5g63UVA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/execa": {"version": "5.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/execa/-/execa-5.1.1.tgz", "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/fastq/-/fastq-1.19.1.tgz", "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/fresh/-/fresh-0.5.2.tgz", "integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/globby/-/globby-11.1.0.tgz", "integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==", "dev": true, "license": "MIT"}, "node_modules/http-assert": {"version": "1.5.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/http-assert/-/http-assert-1.5.0.tgz", "integrity": "sha512-uPpH7OKX4H25hBmU6G1jWNaqJGpTXxey+YOUizJUAgu0AjLUeC8D73hTrhvDS5D+GJN1DN1+hhc/eF/wpxtp0w==", "dev": true, "license": "MIT", "dependencies": {"deep-equal": "~1.0.1", "http-errors": "~1.8.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-errors": {"version": "1.8.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/http-errors/-/http-errors-1.8.1.tgz", "integrity": "sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-errors/node_modules/depd": {"version": "1.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/depd/-/depd-1.1.2.tgz", "integrity": "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ignore/-/ignore-5.3.2.tgz", "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/inflation": {"version": "2.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/inflation/-/inflation-2.1.0.tgz", "integrity": "sha512-t54PPJHG1Pp7VQvxyVCJ9mBbjG3Hqryges9bXoOO6GExCPa+//i/d5GSuFtpx3ALLd7lgIAur6zrIlBQyJuMlQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true, "license": "ISC"}, "node_modules/internal-ip": {"version": "6.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/internal-ip/-/internal-ip-6.2.0.tgz", "integrity": "sha512-D8WGsR6yDt8uq7vDMu7mjcR+yRMm3dW8yufyChmszWRjcSHuxLBkR3GdS2HZAjodsaGuCvXeEJpueisXJULghg==", "dev": true, "license": "MIT", "dependencies": {"default-gateway": "^6.0.0", "ipaddr.js": "^1.9.1", "is-ip": "^3.1.0", "p-event": "^4.2.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/internal-ip?sponsor=1"}}, "node_modules/intl-format-cache": {"version": "4.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/intl-format-cache/-/intl-format-cache-4.3.1.tgz", "integrity": "sha512-OEUYNA7D06agqPOYhbTkl0T8HA3QKSuwWh1HiClEnpd9vw7N+3XsQt5iZ0GUEchp5CW1fQk/tary+NsbF3yQ1Q==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/intl-messageformat": {"version": "8.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/intl-messageformat/-/intl-messageformat-8.0.0.tgz", "integrity": "sha512-eiElylUWAV6S8TYIRGUPfv4mUDTzPSprxmPESTikZ/f/lq29Czz+hwusO7k4vozKke+NOZyj/aPGW6qpd4ms4Q==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"intl-format-cache": "^4.2.21", "intl-messageformat-parser": "^4.0.0"}}, "node_modules/intl-messageformat-parser": {"version": "4.1.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/intl-messageformat-parser/-/intl-messageformat-parser-4.1.4.tgz", "integrity": "sha512-zV4kBUD1yhxSyaXm6bGhmP4HFH9Gh4pRQwNn+xq5P+B1dT8mpaAfU75nfUn4HgddIB6pyFnzM5MQjO55UpJwkQ==", "deprecated": "We've written a new parser that's 6x faster and is backwards compatible. Please use @formatjs/icu-messageformat-parser", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@formatjs/intl-unified-numberformat": "^3.3.3"}}, "node_modules/ip-regex": {"version": "4.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ip-regex/-/ip-regex-4.3.0.tgz", "integrity": "sha512-B9ZWJxHHOHUhUjCPrMpLD4xEq35bUTClHM1S6CBU5ixQnkZmwipwgc96vAd7AAGM9TGHvJR+Uss+/Ak6UphK+Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-docker": {"version": "2.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-ip": {"version": "3.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-ip/-/is-ip-3.1.0.tgz", "integrity": "sha512-35vd5necO7IitFPjd/YBeqwWnyDWbuLH9ZXQdMfDA8TEo7pv5X8yfrvVO3xbJbLUlERCMvf6X0hTUamQxCYJ9Q==", "dev": true, "license": "MIT", "dependencies": {"ip-regex": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-wsl": {"version": "2.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isbinaryfile": {"version": "5.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/isbinaryfile/-/isbinaryfile-5.0.4.tgz", "integrity": "sha512-YKBKVkKhty7s8rxddb40oOkuP0NbaeXrQvLin6QMHL7Ypiy2RW9LwOVrVgZRyOrhQlayMd9t+D8yDy8MKFTSDQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 18.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true, "license": "ISC"}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "integrity": "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/keygrip": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/keygrip/-/keygrip-1.1.0.tgz", "integrity": "sha512-iYSchDJ+liQ8iwbSI2QqsQOvqv58eJCEanyJPJi+Khyu8smkcKSFUCbPwzFcL7YVtZ6eONjqRX/38caJ7QjRAQ==", "dev": true, "license": "MIT", "dependencies": {"tsscmp": "1.0.6"}, "engines": {"node": ">= 0.6"}}, "node_modules/koa": {"version": "2.16.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/koa/-/koa-2.16.1.tgz", "integrity": "sha512-umfX9d3iuSxTQP4pnzLOz0HKnPg0FaUUIKcye2lOiz3KPu1Y3M3xlz76dISdFPQs37P9eJz1wUpcTS6KDPn9fA==", "dev": true, "license": "MIT", "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.9.0", "debug": "^4.3.2", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^2.0.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "engines": {"node": "^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4"}}, "node_modules/koa-compose": {"version": "4.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/koa-compose/-/koa-compose-4.1.0.tgz", "integrity": "sha512-8ODW8TrDuMYvXRwra/Kh7/rJo9BtOfPc6qO8eAfC80CnCvSjSl0bkRM24X6/XBBEyj0v1nRUQ1LyOy3dbqOWXw==", "dev": true, "license": "MIT"}, "node_modules/koa-convert": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/koa-convert/-/koa-convert-2.0.0.tgz", "integrity": "sha512-asOvN6bFlSnxewce2e/DK3p4tltyfC4VM7ZwuTuepI7dEQVcvpyFuBcEARu1+Hxg8DIwytce2n7jrZtRlPrARA==", "dev": true, "license": "MIT", "dependencies": {"co": "^4.6.0", "koa-compose": "^4.1.0"}, "engines": {"node": ">= 10"}}, "node_modules/koa-etag": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/koa-etag/-/koa-etag-4.0.0.tgz", "integrity": "sha512-1cSdezCkBWlyuB9l6c/IFoe1ANCDdPBxkDkRiaIup40xpUub6U/wwRXoKBZw/O5BifX9OlqAjYnDyzM6+l+TAg==", "dev": true, "license": "MIT", "dependencies": {"etag": "^1.8.1"}}, "node_modules/koa-send": {"version": "5.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/koa-send/-/koa-send-5.0.1.tgz", "integrity": "sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "http-errors": "^1.7.3", "resolve-path": "^1.4.0"}, "engines": {"node": ">= 8"}}, "node_modules/koa-static": {"version": "5.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/koa-static/-/koa-static-5.0.0.tgz", "integrity": "sha512-UqyYyH5YEXaJrf9S8E23GoJFQZXkBVJ9zYYMPGz919MSX1KuvAcycIuS0ci150HCoPf4XQVhQ84Qf8xRPWxFaQ==", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.1.0", "koa-send": "^5.0.0"}, "engines": {"node": ">= 7.6.0"}}, "node_modules/koa-static/node_modules/debug": {"version": "3.2.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/linkify-it": {"version": "5.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/linkify-it/-/linkify-it-5.0.0.tgz", "integrity": "sha512-5aHCbzQRADcdP+ATqnDuhhJ/MRIqDkZX5pyjFHRRysS8vZ5AbqGEoFIb6pYHPZ+L/OC2Lc+xT8uHVVR5CAK/wQ==", "dev": true, "license": "MIT", "dependencies": {"uc.micro": "^2.0.0"}}, "node_modules/lit": {"version": "3.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lit/-/lit-3.3.1.tgz", "integrity": "sha512-Ksr/8L3PTapbdXJCk+EJVB78jDodUMaP54gD24W186zGRARvwrsPfS60wae/SSCTCNZVPd1chXqio1qHQmu4NA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@lit/reactive-element": "^2.1.0", "lit-element": "^4.2.0", "lit-html": "^3.3.0"}}, "node_modules/lit-element": {"version": "2.5.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lit-element/-/lit-element-2.5.1.tgz", "integrity": "sha512-ogu7PiJTA33bEK0xGu1dmaX5vhcRjBXCFexPja0e7P7jqLhTpNKYRPmE+GmiCaRVAbiQKGkUgkh/i6+bh++dPQ==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"lit-html": "^1.1.1"}}, "node_modules/lit-html": {"version": "1.4.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lit-html/-/lit-html-1.4.1.tgz", "integrity": "sha512-B9btcSgPYb1q4oSOb/PrOT6Z/H+r6xuNzfH4lFli/AWhYwdtrgQkQWBbIc6mdnf6E2IL3gDXdkkqNktpU0OZQA==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/lit/node_modules/lit-element": {"version": "4.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lit-element/-/lit-element-4.2.1.tgz", "integrity": "sha512-WGAWRGzirAgyphK2urmYOV72tlvnxw7YfyLDgQ+OZnM9vQQBQnumQ7jUJe6unEzwGU3ahFOjuz1iz1jjrpCPuw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@lit-labs/ssr-dom-shim": "^1.4.0", "@lit/reactive-element": "^2.1.0", "lit-html": "^3.3.0"}}, "node_modules/lit/node_modules/lit-html": {"version": "3.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lit-html/-/lit-html-3.3.1.tgz", "integrity": "sha512-S9hbyDu/vs1qNrithiNyeyv64c9yqiW9l+DBgI18fL+MTvOtWoFR0FWiyq1TxaYef5wNlpEmzlXoBlZEO+WjoA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@types/trusted-types": "^2.0.2"}}, "node_modules/log-update": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/log-update/-/log-update-4.0.0.tgz", "integrity": "sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.3.0", "cli-cursor": "^3.1.0", "slice-ansi": "^4.0.0", "wrap-ansi": "^6.2.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lru-cache": {"version": "8.0.5", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/lru-cache/-/lru-cache-8.0.5.tgz", "integrity": "sha512-MhWWlVnuab1RG5/zMRRcVGXZLCXrZTgfwMikgzCegsPnG62yDQo5JnqKkrK4jO5iKqDAZGItAqN5CtKBCBWRUA==", "dev": true, "license": "ISC", "engines": {"node": ">=16.14"}}, "node_modules/make-dir": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/make-dir/-/make-dir-4.0.0.tgz", "integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/markdown-it": {"version": "14.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/markdown-it/-/markdown-it-14.1.0.tgz", "integrity": "sha512-a54IwgWPaeBCAAsv13YgmALOF1elABB08FxO9i+r4VFk5Vl4pKokRPeX8u5TCgSsPi6ec1otfLjdOpVcgbpshg==", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1", "entities": "^4.4.0", "linkify-it": "^5.0.0", "mdurl": "^2.0.0", "punycode.js": "^2.3.1", "uc.micro": "^2.1.0"}, "bin": {"markdown-it": "bin/markdown-it.mjs"}}, "node_modules/markdown-it-link-attributes": {"version": "4.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/markdown-it-link-attributes/-/markdown-it-link-attributes-4.0.1.tgz", "integrity": "sha512-pg5OK0jPLg62H4k7M9mRJLT61gUp9nvG0XveKYHMOOluASo9OEF13WlXrpAp2aj35LbedAy3QOCgQCw0tkLKAQ==", "dev": true, "license": "MIT"}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/mdurl": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/mdurl/-/mdurl-2.0.0.tgz", "integrity": "sha512-Lf+9+2r+Tdp5wXDXC4PcIBjTDtq4UKjCPMQhKIuzpJNW0b96kVqSwW0bT7FhRSfmAiFYgP+SCRvdrDozfh0U5w==", "dev": true, "license": "MIT"}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/nanocolors": {"version": "0.2.13", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/nanocolors/-/nanocolors-0.2.13.tgz", "integrity": "sha512-0n3mSAQLPpGLV9ORXT5+C/D4mwew7Ebws69Hx4E2sgz2ZA5+32Q80B9tL8PbL7XHnRDiAxH/pnrUJ9a4fkTNTA==", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/onetime/-/onetime-5.1.2.tgz", "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/only": {"version": "0.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/only/-/only-0.0.2.tgz", "integrity": "sha512-Fvw+Jemq5fjjyWz6CpKx6w9s7xxqo3+JCyM0WXWeCSOboZ8ABkyvP8ID4CZuChA/wxSx+XSJmdOm8rGVyJ1hdQ==", "dev": true}, "node_modules/open": {"version": "8.4.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/open/-/open-8.4.2.tgz", "integrity": "sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==", "dev": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-event": {"version": "4.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/p-event/-/p-event-4.2.0.tgz", "integrity": "sha512-KXatOjCRXXkSePPb1Nbi0p0m+gQAwdlbhi4wQKJPI1HsMQS9g+Sqp2o+QHziPr7eYJyOZet836KoHEVM1mwOrQ==", "dev": true, "license": "MIT", "dependencies": {"p-timeout": "^3.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-timeout": {"version": "3.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/p-timeout/-/p-timeout-3.2.0.tgz", "integrity": "sha512-rhIwUycgwwKcP9yTOOFK/AKsAopjjCakVqLHePO3CC6Mir1Z99xT+R63jZxAT5lFZLa2inS5h+ZS2GvR99/FBg==", "dev": true, "license": "MIT", "dependencies": {"p-finally": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/parse5": {"version": "6.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/parse5/-/parse5-6.0.1.tgz", "integrity": "sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==", "dev": true, "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/prettier": {"version": "3.6.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/prettier/-/prettier-3.6.2.tgz", "integrity": "sha512-I7AIg5boAr5R0FFtJ6rCfD+LFsWHp81dolrFD8S79U9tb8Az2nGrJncnMSnys+bpQJfRUzqs9hnA81OAA3hCuQ==", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prismjs": {"version": "1.30.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/prismjs/-/prismjs-1.30.0.tgz", "integrity": "sha512-DEvV2ZF2r2/63V+tK8hQvrR2ZGn10srHbXviTlcv7Kpzw8jWiNTqbVgjO3IY8RxrrOUF8VPMQQFysYYYv0YZxw==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/punycode.js": {"version": "2.3.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/punycode.js/-/punycode.js-2.3.1.tgz", "integrity": "sha512-uxFIHU0YlHYhDQtV4R9J6a52SLx28BCjT+4ieh7IGbgwVJWO+km431c4yRlREUAsAmt/uMjQUyQHNEPf0M39CA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/http-errors": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "dev": true, "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/statuses": {"version": "2.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/readdirp": {"version": "4.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/readdirp/-/readdirp-4.1.2.tgz", "integrity": "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==", "dev": true, "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/resolve-path": {"version": "1.4.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/resolve-path/-/resolve-path-1.4.0.tgz", "integrity": "sha512-i1xevIst/Qa+nA9olDxLWnLk8YZbi8R/7JPbCMcgyWaFR6bKWaexgJgEB5oc2PKMjYdrHynyz0NY+if+H98t1w==", "dev": true, "license": "MIT", "dependencies": {"http-errors": "~1.6.2", "path-is-absolute": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/resolve-path/node_modules/depd": {"version": "1.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/depd/-/depd-1.1.2.tgz", "integrity": "sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/resolve-path/node_modules/http-errors": {"version": "1.6.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/resolve-path/node_modules/inherits": {"version": "2.0.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/inherits/-/inherits-2.0.3.tgz", "integrity": "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==", "dev": true, "license": "ISC"}, "node_modules/resolve-path/node_modules/setprototypeof": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==", "dev": true, "license": "ISC"}, "node_modules/restore-cursor": {"version": "3.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/restore-cursor/-/restore-cursor-3.1.0.tgz", "integrity": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/reusify/-/reusify-1.1.0.tgz", "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "6.6.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/rxjs/-/rxjs-6.6.7.tgz", "integrity": "sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true, "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "dev": true, "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true, "license": "ISC"}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "4.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/slice-ansi/-/slice-ansi-4.0.0.tgz", "integrity": "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/source-map": {"version": "0.7.4", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/source-map/-/source-map-0.7.4.tgz", "integrity": "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/statuses": {"version": "1.5.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/statuses/-/statuses-1.5.0.tgz", "integrity": "sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/style-mod": {"version": "4.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/style-mod/-/style-mod-4.1.2.tgz", "integrity": "sha512-wnD1HyVqpJUI2+eKZ+eo1UwghftP6yuFheBqqe+bWCotBjC2K1YnteJILRMs3SM4V/0dLEW1SC27MWP5y+mwmw==", "dev": true, "license": "MIT"}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tslib": {"version": "1.14.1", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "license": "0BSD"}, "node_modules/tsscmp": {"version": "1.0.6", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/tsscmp/-/tsscmp-1.0.6.tgz", "integrity": "sha512-LxhtAkPDTkVCMQjt2h6eBVY28KCjikZqZfMcC15YBeNjkgUpdCfBu5HoiOTDu86v6smE8yOjyEktJ8hlbANHQA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.x"}}, "node_modules/type-fest": {"version": "0.21.3", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/uc.micro": {"version": "2.1.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/uc.micro/-/uc.micro-2.1.0.tgz", "integrity": "sha512-ARDJmphmdvUk6Glw7y9DQ2bFkKBHwQHLi2lsaH6PPmz/Ka9sFOBsBluozhDltWmnv9u/cF6Rt87znRTPV+yp/A==", "dev": true, "license": "MIT"}, "node_modules/undici-types": {"version": "7.8.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/undici-types/-/undici-types-7.8.0.tgz", "integrity": "sha512-9UJ2xGDvQ43tYyVMpuHlsgApydB8ZKfVYTsLDhXkFL/6gfkp+U8xTGdh8pMJv1SpZna0zxG1DwsKZsreLbXBxw==", "dev": true, "license": "MIT"}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/w3c-keyname": {"version": "2.2.8", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/w3c-keyname/-/w3c-keyname-2.2.8.tgz", "integrity": "sha512-dpojBhNsCNN7T82Tm7k26A6G9ML3NkhDsnw9n/eoxSRlVBB4CEtIQ/KTCLI2Fwf3ataSXRhYFkQi3SlnFwPvPQ==", "dev": true, "license": "MIT"}, "node_modules/which": {"version": "2.0.2", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wrap-ansi": {"version": "6.2.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ws": {"version": "7.5.10", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ws/-/ws-7.5.10.tgz", "integrity": "sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/ylru": {"version": "1.4.0", "resolved": "https://artifactory.globaldevtools.bbva.com/artifactory/api/npm/gl-bbva-npm-virtual/ylru/-/ylru-1.4.0.tgz", "integrity": "sha512-2OQsPNEmBCvXuFlIni/a+Rn+R2pHW9INm0BxXJ4hVDA8TirqMj+J/Rp9ItLatT/5pZqWwefVrTQcHpixsxnVlA==", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}}}