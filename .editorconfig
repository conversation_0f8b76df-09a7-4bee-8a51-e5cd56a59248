# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# editorconfig.org

root = true

[*]
indent_style = space
indent_size = 2
quote_type = single
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.md]
trim_trailing_whitespace = false

[*.json]
insert_final_newline = ignore
indent_size = 2

[*.{html,js,md}]
block_comment_start = /**
block_comment = *
block_comment_end = */

[**.min.js]
indent_style = ignore
insert_final_newline = ignore
